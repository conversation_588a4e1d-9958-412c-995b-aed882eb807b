<mxfile host="app.diagrams.net" modified="2025-05-06T01:05:18.321Z" agent="Mozilla/5.0 (X11; Linux x86_64)" version="22.1.18" type="device">
  <diagram name="Page-1" id="search-server-architecture">
    <mxGraphModel dx="1422" dy="826" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1100" pageHeight="850" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Main Application Framework -->
        <mxCell id="2" value="Main Application Framework" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="420" y="20" width="270" height="110" as="geometry" />
        </mxCell>
        <mxCell id="3" value="server.py" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1" parent="2" vertex="1">
          <mxGeometry y="30" width="270" height="30" as="geometry" />
        </mxCell>
        <mxCell id="4" value="Main entrypoint, component initialization, FastMCP server config, routes and tool registration" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="2" vertex="1">
          <mxGeometry y="60" width="270" height="50" as="geometry" />
        </mxCell>
        
        <!-- Server Configuration -->
        <mxCell id="5" value="Server Configuration" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="420" y="170" width="270" height="120" as="geometry" />
        </mxCell>
        <mxCell id="6" value="server_config.py" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1" parent="5" vertex="1">
          <mxGeometry y="30" width="270" height="30" as="geometry" />
        </mxCell>
        <mxCell id="7" value="Environment variable validation, OAuth configuration, default client creation, server lifecycle management" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="5" vertex="1">
          <mxGeometry y="60" width="270" height="60" as="geometry" />
        </mxCell>
        
        <!-- Authentication Module -->
        <mxCell id="8" value="Authentication Module" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="60" y="170" width="300" height="150" as="geometry" />
        </mxCell>
        <mxCell id="9" value="auth/google_provider.py" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1" parent="8" vertex="1">
          <mxGeometry y="30" width="300" height="30" as="geometry" />
        </mxCell>
        <mxCell id="10" value="auth/callback_handlers.py" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1" parent="8" vertex="1">
          <mxGeometry y="60" width="300" height="30" as="geometry" />
        </mxCell>
        <mxCell id="11" value="auth/error_handlers.py" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1" parent="8" vertex="1">
          <mxGeometry y="90" width="300" height="30" as="geometry" />
        </mxCell>
        <mxCell id="12" value="OAuth implementation, token management, callback processing, error handling" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="8" vertex="1">
          <mxGeometry y="120" width="300" height="30" as="geometry" />
        </mxCell>
        
        <!-- Middleware Module -->
        <mxCell id="13" value="Middleware Module" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="60" y="350" width="300" height="120" as="geometry" />
        </mxCell>
        <mxCell id="14" value="middleware/sse_middleware.py" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1" parent="13" vertex="1">
          <mxGeometry y="30" width="300" height="30" as="geometry" />
        </mxCell>
        <mxCell id="15" value="Token management, auth token caching, Claude client identification, SSE connection handling, debugging middleware" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="13" vertex="1">
          <mxGeometry y="60" width="300" height="60" as="geometry" />
        </mxCell>
        
        <!-- Routes Module -->
        <mxCell id="16" value="Routes Module" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="60" y="500" width="300" height="120" as="geometry" />
        </mxCell>
        <mxCell id="17" value="routes/auth_routes.py" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1" parent="16" vertex="1">
          <mxGeometry y="30" width="300" height="30" as="geometry" />
        </mxCell>
        <mxCell id="18" value="routes/debug_routes.py" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1" parent="16" vertex="1">
          <mxGeometry y="60" width="300" height="30" as="geometry" />
        </mxCell>
        <mxCell id="19" value="Authentication routes, OAuth callback, debug endpoints, status pages, SSE testing endpoints" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="16" vertex="1">
          <mxGeometry y="90" width="300" height="30" as="geometry" />
        </mxCell>
        
        <!-- Claude Integration Module -->
        <mxCell id="20" value="Claude Integration Module" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="750" y="170" width="290" height="190" as="geometry" />
        </mxCell>
        <mxCell id="21" value="claude_integration.py" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1" parent="20" vertex="1">
          <mxGeometry y="30" width="290" height="30" as="geometry" />
        </mxCell>
        <mxCell id="22" value="claude_init_handler.py" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1" parent="20" vertex="1">
          <mxGeometry y="60" width="290" height="30" as="geometry" />
        </mxCell>
        <mxCell id="23" value="sse_handler.py" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1" parent="20" vertex="1">
          <mxGeometry y="90" width="290" height="30" as="geometry" />
        </mxCell>
        <mxCell id="24" value="sse_app.py" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1" parent="20" vertex="1">
          <mxGeometry y="120" width="290" height="30" as="geometry" />
        </mxCell>
        <mxCell id="25" value="Claude client authentication, session management, SSE event streaming, initialization handlers, message processing" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="20" vertex="1">
          <mxGeometry y="150" width="290" height="40" as="geometry" />
        </mxCell>
        
        <!-- Core API Services -->
        <mxCell id="26" value="Core API Services" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="400" y="350" width="320" height="210" as="geometry" />
        </mxCell>
        <mxCell id="27" value="search_engine.py" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1" parent="26" vertex="1">
          <mxGeometry y="30" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="28" value="web_scraper.py" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1" parent="26" vertex="1">
          <mxGeometry y="60" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="29" value="knowledge_base.py" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1" parent="26" vertex="1">
          <mxGeometry y="90" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="30" value="finnhub_api.py" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1" parent="26" vertex="1">
          <mxGeometry y="120" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="31" value="groundx_api.py" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1" parent="26" vertex="1">
          <mxGeometry y="150" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="32" value="Web search engines, URL content scraping, knowledge base querying, external API integrations" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="26" vertex="1">
          <mxGeometry y="180" width="320" height="30" as="geometry" />
        </mxCell>
        
        <!-- Utility Services -->
        <mxCell id="33" value="Utility Services" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="400" y="600" width="320" height="120" as="geometry" />
        </mxCell>
        <mxCell id="34" value="markdown_to_pdf.py" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1" parent="33" vertex="1">
          <mxGeometry y="30" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="35" value="pandoc_markdown_to_pdf.py" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1" parent="33" vertex="1">
          <mxGeometry y="60" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="36" value="Markdown to PDF conversion, formatting, LaTeX formula handling" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="33" vertex="1">
          <mxGeometry y="90" width="320" height="30" as="geometry" />
        </mxCell>
        
        <!-- Tool Registration Modules -->
        <mxCell id="37" value="Tool Registration Modules" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="750" y="400" width="290" height="180" as="geometry" />
        </mxCell>
        <mxCell id="38" value="tools/search_tools.py" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1" parent="37" vertex="1">
          <mxGeometry y="30" width="290" height="30" as="geometry" />
        </mxCell>
        <mxCell id="39" value="tools/finnhub_tools.py" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1" parent="37" vertex="1">
          <mxGeometry y="60" width="290" height="30" as="geometry" />
        </mxCell>
        <mxCell id="40" value="tools/groundx_tools.py" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1" parent="37" vertex="1">
          <mxGeometry y="90" width="290" height="30" as="geometry" />
        </mxCell>
        <mxCell id="41" value="tools/pdf_tools.py" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1" parent="37" vertex="1">
          <mxGeometry y="120" width="290" height="30" as="geometry" />
        </mxCell>
        <mxCell id="42" value="tools/utility_tools.py" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=1" parent="37" vertex="1">
          <mxGeometry y="150" width="290" height="30" as="geometry" />
        </mxCell>
        
        <!-- Utility Components -->
        <mxCell id="43" value="Utility Components" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" parent="1" vertex="1">
          <mxGeometry x="750" y="620" width="290" height="60" as="geometry" />
        </mxCell>
        <mxCell id="44" value="logger.py - Centralized logging configuration" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="43" vertex="1">
          <mxGeometry y="30" width="290" height="30" as="geometry" />
        </mxCell>
        
        <!-- Relationships -->
        <!-- Main Application to Other Components -->
        <mxCell id="45" value="" style="endArrow=block;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;endFill=1;strokeWidth=2;" parent="1" source="2" target="5" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="440" y="340" as="sourcePoint" />
            <mxPoint x="490" y="290" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="46" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=1;edgeStyle=orthogonalEdgeStyle;" parent="1" source="3" target="8" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="440" y="340" as="sourcePoint" />
            <mxPoint x="490" y="290" as="targetPoint" />
            <Array as="points">
              <mxPoint x="210" y="65" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="47" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=1;edgeStyle=orthogonalEdgeStyle;" parent="1" source="3" target="20" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="440" y="340" as="sourcePoint" />
            <mxPoint x="490" y="290" as="targetPoint" />
            <Array as="points">
              <mxPoint x="895" y="65" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="48" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=1;" parent="1" source="8" target="13" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="440" y="340" as="sourcePoint" />
            <mxPoint x="490" y="290" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="49" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=1;" parent="1" source="13" target="16" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="440" y="340" as="sourcePoint" />
            <mxPoint x="490" y="290" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="50" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=1;" parent="1" source="5" target="26" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="440" y="340" as="sourcePoint" />
            <mxPoint x="490" y="290" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="51" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=1;" parent="1" source="26" target="33" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="440" y="540" as="sourcePoint" />
            <mxPoint x="490" y="490" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="52" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=1;" parent="1" source="20" target="37" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="440" y="440" as="sourcePoint" />
            <mxPoint x="490" y="390" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="53" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=1;" parent="1" source="37" target="43" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="440" y="540" as="sourcePoint" />
            <mxPoint x="490" y="490" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="54" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=1;dashed=1;" parent="1" source="38" target="27" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="440" y="540" as="sourcePoint" />
            <mxPoint x="490" y="490" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="55" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=1;dashed=1;" parent="1" source="39" target="30" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="440" y="540" as="sourcePoint" />
            <mxPoint x="490" y="490" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="56" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=1;dashed=1;" parent="1" source="40" target="31" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="440" y="640" as="sourcePoint" />
            <mxPoint x="490" y="590" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="57" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=1;dashed=1;edgeStyle=orthogonalEdgeStyle;" parent="1" source="41" target="34" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="440" y="640" as="sourcePoint" />
            <mxPoint x="490" y="590" as="targetPoint" />
            <Array as="points">
              <mxPoint x="740" y="535" />
              <mxPoint x="740" y="645" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="58" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.25;entryDx=0;entryDy=0;strokeWidth=1;dashed=1;" parent="1" source="23" target="13" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="440" y="340" as="sourcePoint" />
            <mxPoint x="490" y="290" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="59" value="Uses" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=2" parent="1" vertex="1">
          <mxGeometry x="640" y="340" width="60" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="60" value="Uses" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=2" parent="1" vertex="1">
          <mxGeometry x="640" y="415" width="60" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="61" value="Uses" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=2" parent="1" vertex="1">
          <mxGeometry x="640" y="460" width="60" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="62" value="Uses" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=2" parent="1" vertex="1">
          <mxGeometry x="640" y="605" width="60" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="63" value="Uses" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=2" parent="1" vertex="1">
          <mxGeometry x="530" y="225" width="60" height="30" as="geometry" />
        </mxCell>
        
        <!-- Legend -->
        <mxCell id="64" value="Legend" style="swimlane;fontStyle=1;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="60" y="650" width="300" height="160" as="geometry" />
        </mxCell>
        <mxCell id="65" value="&lt;span style=&quot;background-color: rgb(218, 232, 252);&quot;&gt;Server Core&lt;/span&gt;&amp;nbsp;- Main server components" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="64" vertex="1">
          <mxGeometry y="30" width="300" height="20" as="geometry" />
        </mxCell>
        <mxCell id="66" value="&lt;span style=&quot;background-color: rgb(213, 232, 212);&quot;&gt;Request Handling&lt;/span&gt;&amp;nbsp;- Auth, middleware, routes" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="64" vertex="1">
          <mxGeometry y="50" width="300" height="20" as="geometry" />
        </mxCell>
        <mxCell id="67" value="&lt;span style=&quot;background-color: rgb(225, 213, 231);&quot;&gt;Claude Integration&lt;/span&gt;&amp;nbsp;- Claude-specific components" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="64" vertex="1">
          <mxGeometry y="70" width="300" height="20" as="geometry" />
        </mxCell>
        <mxCell id="68" value="&lt;span style=&quot;background-color: rgb(255, 242, 204);&quot;&gt;Service APIs&lt;/span&gt;&amp;nbsp;- Core service components" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="64" vertex="1">
          <mxGeometry y="90" width="300" height="20" as="geometry" />
        </mxCell>
        <mxCell id="69" value="&lt;span style=&quot;background-color: rgb(255, 230, 204);&quot;&gt;Tool Registration&lt;/span&gt;&amp;nbsp;- Interface registration for services" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="64" vertex="1">
          <mxGeometry y="110" width="300" height="20" as="geometry" />
        </mxCell>
        <mxCell id="70" value="&lt;span style=&quot;background-color: rgb(245, 245, 245);&quot;&gt;Utility Components&lt;/span&gt;&amp;nbsp;- Shared utilities" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="64" vertex="1">
          <mxGeometry y="130" width="300" height="30" as="geometry" />
        </mxCell>
        
        <!-- Data Flow Arrows -->
        <mxCell id="71" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.75;exitY=0;exitDx=0;exitDy=0;entryX=0.75;entryY=1;entryDx=0;entryDy=0;strokeWidth=1;fillColor=#f8cecc;strokeColor=#b85450;dashed=1;" parent="1" source="37" target="25" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="440" as="sourcePoint" />
            <mxPoint x="650" y="390" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="72" value="API Responses" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=2;rotation=270;" parent="1" vertex="1">
          <mxGeometry x="940" y="320" width="60" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="73" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.75;exitDx=0;exitDy=0;entryX=1;entryY=0.75;entryDx=0;entryDy=0;strokeWidth=1;fillColor=#f8cecc;strokeColor=#b85450;dashed=1;" parent="1" source="37" target="26" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="540" as="sourcePoint" />
            <mxPoint x="650" y="490" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="74" value="API Calls" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=2" parent="1" vertex="1">
          <mxGeometry x="640" y="520" width="60" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="75" value="External APIs" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="400" y="750" width="320" height="80" as="geometry" />
        </mxCell>
        <mxCell id="76" value="Search Engines, Finnhub Financial Data, GroundX Document Storage, Jina Scraping API" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="75" vertex="1">
          <mxGeometry y="30" width="320" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="77" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=1;dashed=1;" parent="1" source="33" target="75" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="640" as="sourcePoint" />
            <mxPoint x="650" y="590" as="targetPoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>