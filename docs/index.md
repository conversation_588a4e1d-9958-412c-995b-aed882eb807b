# Tutorial: claude-mcp

`claude-mcp` 项目是一个智能化的**后端服务系统**，扮演着一个*中央调度员*的角色。
它内置了一个核心的**服务编排器** (0)，该编排器负责接收指令，并指挥项目中集成的各种*专业工具模块*来协同完成特定任务。
这些工具包括：用于从互联网搜集信息的**网络信息搜集器** (1)，专门处理和分析财经数据的**金融数据分析助手** (2)，负责管理云端和本地文档资料的**知识与文档管理核心** (3)，以及能够将Markdown格式的笔记轻松转换成PDF文件的**Markdown转PDF生成器** (4)。
通过这些模块的精密配合，项目为AI助手等外部系统提供了强大的信息处理和任务执行能力。


**Source Repository:** [https://github.com/netcaster1/claude-mcp](https://github.com/netcaster1/claude-mcp)

```mermaid
flowchart TD
    A0["服务编排器
"]
    A1["网络信息搜集器
"]
    A2["金融数据分析助手
"]
    A3["知识与文档管理核心
"]
    A4["Markdown转PDF生成器
"]
    A0 -- "调用搜集" --> A1
    A0 -- "调用分析" --> A2
    A0 -- "调用管理" --> A3
    A0 -- "调用生成" --> A4
```

## Chapters

1. [服务编排器
](01_服务编排器_.md)
2. [网络信息搜集器
](02_网络信息搜集器_.md)
3. [金融数据分析助手
](03_金融数据分析助手_.md)
4. [知识与文档管理核心
](04_知识与文档管理核心_.md)
5. [Markdown转PDF生成器
](05_markdown转pdf生成器_.md)


---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)