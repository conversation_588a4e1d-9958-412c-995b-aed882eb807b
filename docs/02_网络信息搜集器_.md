# Chapter 2: 网络信息搜集器


在上一章 [服务编排器](01_服务编排器_.md) 中，我们了解了 `claude-mcp` 项目的“大脑”——服务编排器。它像一个聪明的指挥家，能够调度各种工具来完成复杂的任务。现在，我们将深入了解服务编排器经常倚仗的一组强大“乐器”：**网络信息搜集器**。

## 为什么需要网络信息搜集器？

想象一下，你是一位研究员，正在探索“人工智能在医疗领域的最新应用”。为了完成这项研究，你可能需要：

1.  在浩瀚的互联网上搜索相关的学术论文、新闻报道、技术博客等。
2.  当你找到一篇看起来非常有价值的文章链接时，你希望能够快速获取它的完整内容，以便仔细阅读和分析。

如果每次都需要手动去各个搜索引擎搜索，然后逐个打开网页复制粘贴内容，那效率也太低了！这时，**网络信息搜集器**就能大显身手了。

它的核心任务就是从互联网上获取信息，自动化地完成上述这些繁琐的工作，让你能更专注于信息本身的价值。

## 网络信息搜集器的两大核心成员

网络信息搜集器主要由两个关键模块组成，它们各司其职，共同完成信息获取的任务：

1.  **`SearchEngine` (搜索引擎模块)**：
    *   **它是什么？** 把它想象成一个“万能电视遥控器”，但控制的不是电视频道，而是各种不同的搜索引擎（比如 Tavily, Google, Serper, Bing 等）。
    *   **它做什么？** 你只需要告诉它你想搜索什么（关键词），以及你想用哪个“频道”（搜索引擎），它就会帮你去那个引擎执行搜索，并把结果整理好给你。

2.  **`WebScraper` (网页抓取器模块)**：
    *   **它是什么？** 这是一个精准的“网页剪刀手”或者“内容提取专家”。
    *   **它做什么？** 当你给它一个具体的网址（URL），它就能像一个勤劳的机器人一样访问这个网页，并智能地提取出网页的主要文本内容，通常还会贴心地将这些内容转换成 Markdown 格式，方便你后续阅读、编辑或进一步处理。

这两个模块协同工作，使得从网上找资料、读资料变得轻松高效。

## 网络信息搜集器是如何工作的？

在 [第 1 章: 服务编排器](01_服务编排器_.md) 中，我们提到服务编排器会定义和调用各种“工具”。其中，`"search"` 工具和 `"scrape_url"` 工具就直接依赖于我们的网络信息搜集器。

*   当服务编排器收到一个执行 `"search"` 工具的请求时，它实际上会唤醒 `SearchEngine` 模块来执行搜索。
*   当服务编排器收到一个执行 `"scrape_url"` 工具的请求时，它会指派 `WebScraper` 模块去抓取指定网页的内容。

接下来，我们就深入这两个模块内部，看看它们具体是怎么实现的。

## 深入探索 `SearchEngine` 模块：你的超级搜索助理

`SearchEngine` 模块的目标是提供一个统一的接口来调用多种搜索引擎。

### 1. 初始化：准备好你的API“钥匙”

要使用各种在线搜索服务，通常需要对应的 API 密钥（可以理解为访问这些服务的“钥匙”）。`SearchEngine` 在初始化时会加载这些密钥。

```python
# 文件: src/search_server/search_engine.py
import os # 用于访问环境变量
from .logger import logger # 项目内部的日志记录器

class SearchEngine:
    def __init__(self):
        # 从环境变量中加载各种搜索引擎的API密钥
        self.tavily_api_key = os.getenv("TAVILY_API_KEY")
        self.serper_api_key = os.getenv("SERPER_API_KEY")
        # ... 此处省略了加载其他引擎（如Bing, Google等）的API密钥的代码 ...
        logger.info("SearchEngine 模块已成功初始化并加载了API密钥。")
```
这段代码展示了 `SearchEngine` 类在创建实例时（`__init__` 方法）如何从系统的环境变量中读取预先配置好的 API 密钥。例如，`os.getenv("TAVILY_API_KEY")` 就是获取名为 `TAVILY_API_KEY` 的环境变量值。

### 2. 统一的搜索入口：`search` 方法

`SearchEngine` 提供了一个核心的 `search` 方法。你告诉它使用哪个引擎 (`engine`) 和搜索什么 (`query`)，它就会去调用相应的内部方法。

```python
# 文件: src/search_server/search_engine.py
# ... (在 SearchEngine 类内部) ...
async def search(self, engine: str, query: str) -> List[Dict[str, Any]]:
    logger.info(f"接收到搜索请求: 引擎='{engine}', 查询='{query}'")
    if engine == "tavily":
        return await self.search_tavily(query) # 调用Tavily搜索
    elif engine == "serper":
        return await self.search_serper(query) # 调用Serper搜索
    # ... 可能还有 elif engine == "google": 等其他引擎的处理 ...
    else:
        logger.warning(f"不支持或未知的搜索引擎: {engine}")
        return [] # 返回空列表表示未找到或失败
```
这个 `search` 方法像一个分发员。它检查 `engine` 参数的值，然后决定调用哪个更具体的搜索方法（例如 `self.search_tavily`）。注意这里的 `async` 和 `await`关键字，表示这些搜索操作是异步执行的，不会阻塞整个程序。

### 3. 与特定搜索引擎对话：以 `search_tavily` 为例

每个具体的搜索方法（如 `search_tavily`）负责与对应的搜索引擎API进行通信。

```python
# 文件: src/search_server/search_engine.py
# ... (在 SearchEngine 类内部) ...
async def search_tavily(self, query: str) -> List[Dict[str, Any]]:
    endpoint = "https://api.tavily.com/search" # Tavily API 的地址
    payload = { # 构建请求体，告诉Tavily要搜索什么
        "query": query,
        "max_results": 5, # 期望获取最多5条结果
        # ... 其他Tavily特定参数 ...
    }
    headers = { # 请求头，包含认证信息
        "Authorization": f"Bearer {self.tavily_api_key}"
    }
    
    # 此处将使用 aiohttp 库异步发送 POST 请求到 endpoint
    # 并处理响应，提取搜索结果 (为了简洁，省略了实际网络请求代码)
    # 模拟获取到的原始结果:
    mock_tavily_response_data = {
        'results': [
            {'content': '关于“人工智能”的Tavily搜索结果摘要1...', 'url': 'http://example.com/ai-article1'},
            {'content': 'Tavily找到的“人工智能”相关信息2...', 'url': 'http://example.com/ai-article2'}
        ]
    }
    
    formatted_results = []
    for result in mock_tavily_response_data.get('results', []): # 遍历原始结果
        formatted_results.append({ # 将结果统一格式化
            "file_name": "Tavily", # 结果来源
            "chunk_text": result.get('content'), # 结果摘要
            "url": result.get('url') # 结果链接
        })
    return formatted_results
```
这段代码（已简化）展示了 `search_tavily` 的核心逻辑：
1.  定义 API 端点 (`endpoint`)、请求体 (`payload`) 和请求头 (`headers`)。API 密钥通常放在请求头中用于认证。
2.  （实际代码中）使用 `aiohttp` 这样的库向 Tavily 服务器发送一个异步 HTTP 请求。
3.  接收到服务器返回的 JSON 数据后，解析它。
4.  将从 Tavily 获取的结果转换成 `claude-mcp` 内部统一的格式（一个包含字典的列表），每个字典代表一条搜索结果，包含来源、内容摘要和URL。

### `SearchEngine` 工作流程小结

下图展示了当服务编排器调用 `search` 工具时，`SearchEngine` 模块内部的大致工作流程：

```mermaid
sequenceDiagram
    participant 用户或AI助手
    participant 服务编排器
    participant SearchEngine模块
    participant 特定搜索引擎API (如Tavily)

    用户或AI助手->>服务编排器: 请求: 搜索("tavily", "AI最新进展")
    服务编排器->>SearchEngine模块: 调用 search("tavily", "AI最新进展")
    SearchEngine模块->>SearchEngine模块: 判断引擎, 调用 search_tavily("AI最新进展")
    SearchEngine模块->>特定搜索引擎API (如Tavily): 发起HTTP API请求 (含查询和密钥)
    特定搜索引擎API (如Tavily)-->>SearchEngine模块: 返回搜索结果 (JSON格式)
    SearchEngine模块-->>SearchEngine模块: 解析并格式化结果
    SearchEngine模块-->>服务编排器: 返回格式化后的结果列表
    服务编排器-->>用户或AI助手: 返回最终搜索结果

```

## 深入探索 `WebScraper` 模块：你的网页内容提取专家

当你通过 `SearchEngine` 找到了一个有价值的网页链接后，就轮到 `WebScraper` 模块出场了。它能帮你“阅读”这个网页并提取出主要内容。`claude-mcp` 使用了 [Jina AI Reader](https://jina.ai/reader/) 服务来实现这个功能，Jina Reader 可以智能地将任何网页URL转换为干净的 Markdown 文本。

### 1. 初始化：准备Jina API密钥

与 `SearchEngine` 类似，`WebScraper` 初始化时也需要加载 Jina Reader API 的密钥。

```python
# 文件: src/search_server/web_scraper.py
import os
from .logger import logger

class WebScraper:
    def __init__(self):
        self.jina_api_key = os.getenv("JINA_API_KEY") # 从环境变量加载Jina API密钥
        if not self.jina_api_key:
            logger.error("JINA_API_KEY 环境变量未设置！WebScraper可能无法工作。")
        # Jina API 要求密钥在请求头中以 Bearer token 形式提供
        self.headers = {
            'Authorization': f'Bearer {self.jina_api_key}'
        }
        logger.info("WebScraper 模块已成功初始化。")
```
这里，`WebScraper` 获取 `JINA_API_KEY` 并预先准备好包含认证信息的 `headers`，方便后续调用 Jina API。

### 2. 抓取网页内容：`scrape_url` 方法

`scrape_url` 方法接收一个 URL，然后调用 Jina Reader API 来获取该 URL 对应网页的 Markdown 内容。

```python
# 文件: src/search_server/web_scraper.py
# ... (在 WebScraper 类内部) ...
import aiohttp # 用于执行异步HTTP请求
from typing import Optional, Dict, Any

async def scrape_url(self, url: str) -> Optional[Dict[str, Any]]:
    # Jina Reader API 的访问方式是在基础URL后拼接目标URL
    # 例如: https://r.jina.ai/https://www.example.com
    jina_reader_url = f'https://r.jina.ai/{url}'
    logger.info(f"准备使用 Jina Reader API 抓取 URL: {url}")

    try:
        # 此处将使用 aiohttp 库异步发送 GET 请求到 jina_reader_url
        # (为了简洁，省略了实际网络请求和完整错误处理代码)
        # async with aiohttp.ClientSession() as session:
        #   async with session.get(jina_reader_url, headers=self.headers, timeout=30) as response:
        #     if response.status == 200:
        #       content_markdown = await response.text(encoding="utf-8")
        #       logger.info(f"成功抓取并转换为Markdown: {url}")
        #       return {'url': url, 'content': content_markdown, 'status': 'success'}
        #     else:
        #       logger.error(f"Jina Reader抓取失败: HTTP {response.status} for {url}")
        #       return {'url': url, 'error': f"HTTP error {response.status}", 'status': 'failed'}
        
        # 模拟成功抓取的情况
        mock_markdown_content = f"# 这是从 {url} 提取的标题\n\n这是模拟的Markdown主要内容。\nJina Reader会尝试去除广告和导航，只保留正文。"
        return {'url': url, 'content': mock_markdown_content, 'status': 'success'}

    except Exception as e: # 捕获其他潜在错误
        logger.error(f"抓取URL {url} 时发生意外错误: {str(e)}")
        return {'url': url, 'error': str(e), 'status': 'failed'}
```
这段简化的代码说明了 `scrape_url` 的工作原理：
1.  根据输入的 `url` 构建 Jina Reader API 的请求地址 `jina_reader_url`。
2.  （实际代码中）使用 `aiohttp` 向 `jina_reader_url` 发送一个携带 `self.headers`（包含 Jina API 密钥）的异步 GET 请求。
3.  如果请求成功 (HTTP 状态码 200)，Jina Reader API 会返回网页主要内容的 Markdown 文本。
4.  方法返回一个字典，其中包含原始 URL、抓取到的内容（`content`）以及状态（`status`）。

### `WebScraper` 工作流程小结

下图描绘了当服务编排器调用 `scrape_url` 工具时，`WebScraper` 模块如何通过 Jina Reader API 获取网页内容：

```mermaid
sequenceDiagram
    participant 用户或AI助手
    participant 服务编排器
    participant WebScraper模块
    participant JinaReaderAPI

    用户或AI助手->>服务编排器: 请求: 抓取("https://example.com/article")
    服务编排器->>WebScraper模块: 调用 scrape_url("https://example.com/article")
    WebScraper模块->>JinaReaderAPI: 发起GET请求 (https://r.jina.ai/https://example.com/article, 含API密钥)
    JinaReaderAPI-->>WebScraper模块: 返回网页主要内容的Markdown文本
    WebScraper模块-->>服务编排器: 返回包含Markdown内容和状态的字典
    服务编排器-->>用户或AI助手: 返回抓取结果
end
```

## 总结

在本章中，我们一起探索了 `claude-mcp` 项目的**网络信息搜集器**。我们了解到：

*   网络信息搜集器由两大主力模块构成：`SearchEngine`（搜索引擎模块）和 `WebScraper`（网页抓取器模块）。
*   `SearchEngine` 像一个**万能遥控器**，能够接入并统一调用多种搜索引擎（如Tavily, Serper, Google等），帮助我们快速在互联网上查找信息。它通过加载各引擎的API密钥，并根据指令选择合适的引擎执行搜索。
*   `WebScraper` 则像一个**精准的网页剪刀手**，利用 Jina Reader API 等服务，能直接访问指定网址，提取其主要内容并转换为易于处理的 Markdown 格式。
*   这两个模块使得 `claude-mcp` 能够高效地从互联网获取原始信息和网页全文，为后续的分析、处理和知识库构建打下坚实基础。

通过了解这些模块的内部工作方式，我们能更好地理解当 [服务编排器](01_服务编排器_.md) 调用 `search` 或 `scrape_url` 等工具时，背后发生了什么。

在下一章，我们将学习另一个有趣的组件：[第 3 章: 金融数据分析助手](03_金融数据分析助手_.md)，看看它是如何帮助我们获取和处理金融市场数据的。

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)