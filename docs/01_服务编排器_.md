# Chapter 1: 服务编排器


欢迎来到 `claude-mcp` 项目的入门教程！在本章中，我们将一起探索整个项目的核心——**服务编排器**。

## 什么是服务编排器？为什么需要它？

想象一下，你正在构建一个非常聪明的 AI 助手。这个助手需要完成各种任务，比如：

*   在互联网上搜索最新的科技新闻。
*   抓取某个网页的详细内容。
*   将一段 Markdown 格式的笔记转换成 PDF 文件。
*   查询某支股票的最新价格。

如果让 AI 助手自己去处理所有这些底层操作的细节，那它很快就会变得非常复杂和难以维护。这时，**服务编排器**就派上用场了！

**服务编排器**就像是 AI 助手的“万能遥控器”和“中央调度室”。AI 助手只需要告诉服务编排器它想做什么（例如，“帮我搜索关于‘人工智能最新进展’的新闻”），服务编排器就会：

1.  **理解请求**：分析 AI 助手想要执行的具体任务。
2.  **选择工具**：从一系列预先定义好的“工具”（如搜索工具、网页抓取工具、PDF 生成工具等）中挑选出合适的那个。
3.  **执行任务**：调用选中的工具来完成任务。
4.  **返回结果**：将执行结果（比如搜索到的新闻列表或生成的 PDF 文件路径）格式化后返回给 AI 助手。

简单来说，服务编排器解决了如何让一个外部系统（如 AI 助手）能够简单、高效地调用一系列复杂功能的问题。它将各种具体的功能封装成独立的“工具”，并提供一个统一的接口来调用这些工具。

在 `claude-mcp` 项目中，这个服务编排器是基于 **MCP (通用控制协议)** 框架构建的。MCP 是一种标准化的方式，用于不同的软件组件之间进行通信和协作。

## 核心概念

在我们深入代码之前，先来了解几个与服务编排器相关的核心概念：

*   **工具 (Tool)**：代表一项具体的功能。例如，“搜索互联网”、“抓取网页内容”、“将 Markdown 转换为 PDF”等都是工具。每个工具都有明确的输入和输出。
*   **请求 (Request)**：当外部系统（如 AI 助手）需要服务编排器执行某项任务时，它会发送一个请求。这个请求通常会指明要使用哪个工具以及执行该工具所需的参数。
*   **响应 (Response)**：服务编排器完成任务后，会将结果打包成一个响应，发送回请求方。
*   **MCP (通用控制协议)**：这是服务编排器与其他系统（包括它管理的工具）进行通信的基础协议。它定义了消息的格式和交互的规则。

## 服务编排器如何工作？一个简单的例子

假设我们的 AI 助手需要搜索关于“气候变化最新研究”的信息，然后将找到的信息整理成一份 PDF 报告。

1.  **AI 助手发送搜索请求**：
    *   AI 助手：“服务编排器，请使用‘搜索’工具，关键词是‘气候变化最新研究’。”
2.  **服务编排器处理搜索请求**：
    *   服务编排器收到请求，找到名为“search”的工具。
    *   它调用这个搜索工具，并将关键词“气候变化最新研究”传递给它。
    *   搜索工具执行搜索，并将结果（例如，一系列相关文章的链接和摘要）返回给服务编排器。
    *   服务编排器将这些搜索结果整理好，返回给 AI 助手。
3.  **AI 助手发送 PDF 生成请求**：
    *   AI 助手（拿到搜索结果后）：“服务编排器，请使用‘Markdown转PDF’工具，标题是‘气候变化研究报告’，内容是[这里是整理好的搜索结果]，文件名是‘climate_research.pdf’。”
4.  **服务编排器处理 PDF 生成请求**：
    *   服务编排器收到请求，找到名为“markdown_to_pdf”的工具。
    *   它调用这个 PDF 生成工具，并将标题、内容和文件名传递给它。
    *   PDF 生成工具将 Markdown 内容转换为 PDF 文件，并保存到指定位置。
    *   PDF 工具将生成结果（例如，文件路径“/reports/climate_research.pdf”）返回给服务编排器。
    *   服务编排器将这个文件路径返回给 AI 助手。

整个过程可以用下面的时序图来表示：

```mermaid
sequenceDiagram
    participant 外部系统 (例如AI助手)
    participant 服务编排器
    participant 搜索工具
    participant PDF生成工具

    外部系统->>服务编排器: 请求执行搜索("search", {query: "气候变化最新研究"})
    服务编排器->>搜索工具: 调用搜索("气候变化最新研究")
    搜索工具-->>服务编排器: 返回搜索结果 (文本)
    服务编排器-->>外部系统: 返回搜索结果

    外部系统->>服务编排器: 请求生成PDF("markdown_to_pdf", {title: "报告", content: "...", filename: "climate.pdf"})
    服务编排器->>PDF生成工具: 调用PDF生成("报告", "...", "climate.pdf")
    PDF生成工具-->>服务编排器: 返回PDF文件路径
    服务编排器-->>外部系统: 返回PDF文件路径
```

## 深入代码：服务编排器的实现

现在，让我们看看 `claude-mcp` 项目中服务编排器是如何通过代码实现的。我们将主要关注 `src/search_server/server.py` 文件。

### 1. 初始化服务编排器

首先，我们需要创建一个服务编排器的实例。

```python
# 文件: src/search_server/server.py
# ... 其他导入 ...
from mcp.server import Server # 导入 Server 类
import mcp.types as types     # 导入 MCP 类型定义

# ... 省略了环境变量加载和其他组件初始化 ...

# 创建服务编排器实例，命名为 "search_server"
server = Server("search_server")
```
这行代码 `server = Server("search_server")` 非常关键。它实例化了 `mcp.server` 模块中的 `Server` 类，这个 `server` 对象就是我们的服务编排器的核心。你可以把它想象成我们刚刚请来的那位“智能总调度”。

### 2. 定义可用的工具

服务编排器需要知道它有哪些“工具”可以用。这是通过 `@server.list_tools()` 装饰器和相应的函数来实现的。

```python
# 文件: src/search_server/server.py

@server.list_tools()
async def handle_list_tools() -> list[types.Tool]:
    """列出所有可用的工具。"""
    logger.debug("正在列出可用工具")
    return [
        types.Tool(  # 定义一个名为 "search" 的工具
            name="search",
            description="使用指定的搜索引擎在互联网上搜索...",
            inputSchema={ # 定义此工具期望的输入参数
                "type": "object",
                "properties": {
                    "engine": { "type": "string", "description": "要使用的搜索引擎" },
                    "query": { "type": "string", "description": "搜索查询的内容" }
                },
                "required": ["engine", "query"], # engine 和 query 是必需的
            },
        ),
        types.Tool( # 定义一个名为 "markdown_to_pdf" 的工具
            name="markdown_to_pdf",
            description="将文本（通常是Markdown格式）转换为PDF文件",
            inputSchema={
                "type": "object",
                "properties": {
                    "title": { "type": "string", "description": "PDF报告的标题" },
                    "content": { "type": "string", "description": "文本文件的内容（Markdown格式）" },
                    "filename": { "type": "string", "description": "PDF文件名" },
                    "use_pandoc": { "type": "boolean", "description": "是否优先使用Pandoc" }
                },
                "required": ["title", "content", "filename", "use_pandoc"],
            },
        ),
        # ... 此处省略了其他工具的定义，例如：
        # scrape_url, knowledge_search, finnhub_symbol_lookup 等等
    ]
```
在 `handle_list_tools` 函数中，我们返回了一个 `types.Tool`对象的列表。每个 `types.Tool` 对象都描述了一个工具：
*   `name`: 工具的唯一名称，例如 "search" 或 "markdown_to_pdf"。
*   `description`: 对工具功能的文字描述。
*   `inputSchema`: 一个 JSON Schema 对象，它详细定义了调用此工具时需要提供哪些输入参数，以及这些参数的类型和格式。例如，"search" 工具需要 "engine" 和 "query" 两个字符串类型的参数。

当外部系统想要知道这个服务编排器能做什么时，它会调用一个特殊命令（MCP 协议中定义的），服务编排器就会执行 `handle_list_tools` 函数，并将这个工具列表返回给外部系统。

### 3. 处理工具调用请求

当外部系统决定使用某个工具时，它会发送一个工具调用请求。服务编排器使用 `@server.call_tool()` 装饰器和相应的函数来处理这些请求。

```python
# 文件: src/search_server/server.py

@server.call_tool()
async def handle_call_tool(
    name: str, arguments: dict | None
) -> list[types.TextContent | types.ImageContent | types.EmbeddedResource]:
    """处理工具执行请求。"""
    logger.info(f"收到工具调用请求: {name}，参数: {arguments}")

    if name == "search":
        engine = arguments.get("engine")
        query = arguments.get("query")
        # ... 此处省略了参数校验 ...
        logger.info(f"执行搜索: 引擎={engine}, 查询={query}")
        # 实际会调用 search_engine.search(...) 来执行搜索
        # results = await search_engine.search(engine, query)
        # 为简化，我们直接返回一个模拟结果
        formatted_results = [
            types.TextContent(type="text", text=f"关于“{query}”的模拟搜索结果来自“{engine}”")
        ]
        return formatted_results

    elif name == "markdown_to_pdf":
        title = arguments.get("title")
        content = arguments.get("content")
        filename = arguments.get("filename")
        use_pandoc = arguments.get("use_pandoc", True) # 获取是否使用 pandoc
        # ... 此处省略了参数校验 ...
        logger.info(f"执行Markdown转PDF: 文件名={filename}, 使用Pandoc={use_pandoc}")
        # 实际会调用 default_pdf_tool.convert_to_pdf(...) 或 pandoc_pdf_tool.convert_to_pdf(...)
        # result = default_pdf_tool.convert_to_pdf(title, content, filename)
        # 为简化，我们直接返回一个模拟结果
        return [
            types.TextContent(type="text", text=f"PDF文件 '{filename}' 已模拟生成。")
        ]
    # ... 此处省略了对其他工具（如 scrape_url, knowledge_search 等）的处理逻辑 ...
    
    else:
        logger.error(f"未知的工具: {name}")
        raise ValueError(f"未知的工具: {name}")
```
`handle_call_tool` 函数接收两个主要参数：
*   `name`: 要调用的工具的名称（例如 "search"）。
*   `arguments`: 一个包含调用该工具所需参数的字典。

函数内部通过 `if/elif/else` 语句来判断请求的是哪个工具，然后执行相应的逻辑。在实际的代码中，这里会调用具体实现该功能的模块。例如：
*   对于 `"search"`，它会调用 `search_engine.search(...)`。这个 `search_engine` 对象是在文件开头初始化的 `SearchEngine` 类的实例，它封装了与各种搜索引擎（Tavily, Serper, Google等）交互的逻辑。我们将在 [第 2 章: 网络信息搜集器](02_网络信息搜集器_.md) 中详细介绍它。
*   对于 `"markdown_to_pdf"`，它会调用 `default_pdf_tool.convert_to_pdf(...)` 或 `pandoc_pdf_tool.convert_to_pdf(...)`。这些是PDF转换工具的实例，具体细节我们会在 [第 5 章: Markdown转PDF生成器](05_markdown转pdf生成器_.md) 中探讨。
*   类似地，金融相关的工具如 `"finnhub_symbol_lookup"` 会调用 `finnhub_api` 模块的功能，这部分内容属于 [第 3 章: 金融数据分析助手](03_金融数据分析助手_.md)。
*   知识库相关的工具如 `"knowledge_search"` 或 GroundX 相关的工具会调用 `knowledge_base` 或 `groundx_api` 的功能，这些是 [第 4 章: 知识与文档管理核心](04_知识与文档管理核心_.md) 的主题。

执行完毕后，`handle_call_tool` 函数会返回一个结果列表。结果的类型可以是文本 (`types.TextContent`)、图片 (`types.ImageContent`) 或其他嵌入式资源。

### 4. 启动服务

最后，`main` 函数负责启动整个服务，使其能够监听并响应外部系统的请求。

```python
# 文件: src/search_server/server.py
# ... （省略了大部分main函数的内容，因为它涉及到stdio和SSE传输的具体设置） ...
import anyio
import click # 用于创建命令行界面

@click.command()
# ... （省略了命令行选项 --port 和 --transport） ...
def main(port: int, transport: str) -> int:
    if transport == "sse":
        # ... SSE (Server-Sent Events) 传输方式的启动逻辑 ...
        logger.info(f"使用 SSE 传输启动服务，端口: {port}")
        # uvicorn.run(app, host="0.0.0.0", port=port) # 简化表示
    else: # 默认为 "stdio"
        logger.info("使用 STDIO 传输启动服务")
        try:
            # anyio.run(run_stdio_server) # 简化表示，run_stdio_server 内部会调用 server.run
            # 实际的 run_stdio_server 会设置 MCP 的输入输出流并运行 server.run
            logger.info("服务编排器（stdio模式）已准备就绪并开始运行。")
            # 这是一个象征性的调用，实际调用在 run_stdio_server 中
            # asyncio.run(server.run(...)) 
        except Exception as e:
            logger.error(f"STDIO 服务启动失败: {str(e)}")
            return 1
    return 0

if __name__ == "__main__":
    main()
```
`main` 函数根据命令行参数选择传输方式（标准输入/输出 `stdio` 或服务器发送事件 `sse`）并启动服务。`server.run(...)` 是 MCP 框架提供的核心方法，它使得服务编排器能够开始监听传入的 MCP 消息，解析它们，调用像 `handle_list_tools` 或 `handle_call_tool` 这样的处理函数，然后将结果发送回去。

## 总结

在本章中，我们了解了**服务编排器**在 `claude-mcp` 项目中的核心作用。它像一个智能总调度，负责：
*   **定义一系列可用的“工具”**，每个工具执行一项特定任务。
*   **接收外部系统的请求**，理解它们想要执行的操作。
*   **调用正确的内部模块或工具**来完成任务。
*   **将结果格式化后返回**给请求方。

我们通过简化的代码示例，看到了服务编排器是如何初始化的，如何定义工具列表，以及如何处理工具调用请求的。服务编排器使得 `claude-mcp` 能够灵活地集成和管理多种功能，为上层应用（如 AI 助手）提供一个统一、简洁的接口。

在下一章中，我们将深入探讨服务编排器所调用的第一个重要工具集：[第 2 章: 网络信息搜集器](02_网络信息搜集器_.md)，看看它是如何实现强大的网络搜索和内容抓取功能的。

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)