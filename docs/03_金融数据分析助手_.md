# Chapter 3: 金融数据分析助手


在上一章 [网络信息搜集器](02_网络信息搜集器_.md) 中，我们探索了 `claude-mcp` 如何从互联网上搜集信息和抓取网页内容。现在，让我们转向一个更专业的领域：金融数据。本章，我们将深入了解 **金融数据分析助手**，它是 `claude-mcp` 项目中负责与金融数据服务平台 Finnhub 沟通的模块。

## 为什么需要金融数据分析助手？

想象一下，你正在开发一个 AI 投资顾问。这个顾问需要实时获取股票价格、公司新闻、财务报告等信息，才能给出明智的投资建议。如果让 AI 助手自己去处理连接各种金融数据源的复杂细节，那将会非常繁琐且容易出错。

这时，**金融数据分析助手** 就应运而生了。它的核心任务是：

*   **简化金融数据获取**：提供简单易用的接口来查询各种金融数据。
*   **对接专业数据源**：与像 Finnhub 这样的专业金融数据平台进行交互。
*   **提供结构化信息**：将获取到的原始数据整理成方便程序处理的格式。

简单来说，金融数据分析助手就像一个内置在 `claude-mcp` 中的财经资讯终端，为所有需要金融数据的场景提供支持。比如，当 AI 助手需要查询某支股票的最新动态时，它就会通过 [服务编排器](01_服务编排器_.md) 来调用这个助手的功能。

## 金融数据分析助手是什么？

在 `claude-mcp` 项目中，金融数据分析助手主要通过一个名为 `FinnhubAPI` 的类来实现。这个类封装了所有与 Finnhub API 交互的细节。Finnhub 是一个提供实时和历史金融市场数据（包括股票、外汇、加密货币等）的平台。

我们的金融数据分析助手主要能做三件事情：

1.  **查询股票代码 (Symbol Lookup)**：当你只知道公司名称或者想模糊搜索某个金融产品时，它能帮你找到最匹配的官方交易代码。
2.  **获取公司新闻 (Company News)**：它可以获取特定公司的最新新闻报道。
3.  **查找SEC备案文件 (SEC Filings)**：它可以查找上市公司在美国证券交易委员会（SEC）提交的官方文件，如年报（10-K）、季报（10-Q）等。

## 金融数据分析助手是如何工作的？

正如我们在 [第 1 章: 服务编排器](01_服务编排器_.md) 中学到的，服务编排器会定义一系列“工具”。金融数据分析助手提供的功能也会被封装成这样的工具。例如，服务编排器可能会定义以下工具：

*   `finnhub_symbol_lookup`: 用于查询股票代码。
*   `finnhub_company_news`: 用于获取公司新闻。
*   `finnhub_sec_filings`: 用于查找SEC备案文件。

当外部系统（比如 AI 助手）需要执行这些金融相关的任务时，它会向服务编排器发送请求。服务编排器接收到请求后，会调用 `FinnhubAPI` 类中对应的方法来完成实际的数据获取工作。

我们来看一个简化的交互流程：

```mermaid
sequenceDiagram
    participant 用户或AI助手
    participant 服务编排器
    participant FinnhubAPI模块 (金融数据分析助手)
    participant Finnhub外部API

    用户或AI助手->>服务编排器: 请求: "帮我查苹果公司的股票代码"
    服务编排器->>服务编排器: 解析请求, 识别为 finnhub_symbol_lookup 工具, 参数: "苹果公司"
    服务编排器->>FinnhubAPI模块 (金融数据分析助手): 调用 symbol_lookup("苹果公司")
    FinnhubAPI模块 (金融数据分析助手)->>Finnhub外部API: 发送HTTP API请求 (含查询和API密钥)
    Finnhub外部API-->>FinnhubAPI模块 (金融数据分析助手): 返回股票代码信息 (JSON格式)
    FinnhubAPI模块 (金融数据分析助手)-->>服务编排器: 返回格式化的代码列表
    服务编排器-->>用户或AI助手: 返回结果: "苹果公司 (AAPL)"

```

## 深入代码：`FinnhubAPI` 类的实现

现在，让我们深入 `src/search_server/finnhub_api.py` 文件，看看金融数据分析助手的核心 `FinnhubAPI` 类是如何实现的。

### 1. 初始化：连接 Finnhub 的准备工作

要使用 Finnhub API，我们首先需要一个 API 密钥。这就像是进入 Finnhub 数据宝库的钥匙。`FinnhubAPI` 类在初始化时会加载这个密钥，并设置好 API 的基础URL。

```python
# 文件: src/search_server/finnhub_api.py
import os
import aiohttp # 用于异步HTTP请求
from typing import Dict, Any, List
from termcolor import colored # 用于在控制台输出彩色日志

class FinnhubAPI:
    def __init__(self):
        """初始化 Finnhub API 工具。"""
        # 从环境变量中加载 FINNHUB_API_KEY
        self.api_key = os.getenv("FINNHUB_API_KEY")
        self.base_url = "https://finnhub.io/api/v1" # Finnhub API 的基础URL
        # 下面这行代码会在工具初始化时打印一条彩色消息到控制台，方便调试
        print(colored("Finnhub API tool initialized", "green"))
```
在这段代码中：
*   `self.api_key = os.getenv("FINNHUB_API_KEY")` 会尝试从系统的环境变量中读取名为 `FINNHUB_API_KEY` 的值。你需要预先设置好这个环境变量，值为你从 Finnhub 官网上获取的 API 密钥。
*   `self.base_url` 定义了所有 Finnhub API 请求的基础路径。
*   `print(colored(...))` 使用了 `termcolor` 库，这只是为了在程序启动时输出一条带颜色的日志，表明模块已成功加载，这对于开发者调试很有帮助。

### 2. 查询股票代码：`symbol_lookup` 方法

这个方法允许你通过公司名称、部分代码或其他关键词来搜索匹配的股票代码。

```python
# 文件: src/search_server/finnhub_api.py
# ... (在 FinnhubAPI 类内部) ...
    async def symbol_lookup(self, query: str) -> Dict[str, Any]:
        """根据查询词搜索最匹配的股票代码。"""
        endpoint = f"{self.base_url}/search" # 股票搜索的API端点
        params = { # 请求参数
            "q": query, # 要搜索的关键词
            "token": self.api_key # 你的API密钥
        }
        
        # 实际代码会使用 aiohttp 发送异步GET请求到 endpoint
        # 并处理响应和可能的错误 (为简洁，此处简化)
        try:
            print(colored(f"模拟查询股票代码: {query}", "cyan")) # 打印调试信息
            # 模拟成功返回一个结果
            if query.lower() == "apple" or query.lower() == "苹果":
                return {
                    "count": 1,
                    "result": [{"description": "APPLE INC", "displaySymbol": "AAPL", "symbol": "AAPL", "type": "Common Stock"}]
                }
            return {"count": 0, "result": []} # 模拟未找到
        except Exception as e:
            # 实际代码会包含更详细的错误处理
            return {"error": str(e), "count": 0, "result": []}
```
这里：
*   `endpoint` 变量组合了基础 URL 和 `/search` 路径，构成了查询股票代码的完整 API 地址。
*   `params` 字典包含了查询所需的参数：`q` 是搜索的关键词，`token` 是我们的 API 密钥。
*   `print(colored(f"Looking up symbol: {query}", "cyan"))` 这行（在提供的原始代码中）也是一个调试打印语句。
*   在实际的 `claude-mcp` 代码中，这里会使用 `aiohttp` 库异步地向 Finnhub 服务器发送一个 GET 请求。如果请求成功，Finnhub 会返回一个 JSON 对象，其中包含了匹配的股票信息列表。
*   为了教程的简洁，我们用一个模拟的输出来代替真实的网络请求。如果查询是 "apple" 或 "苹果"，它会返回一个包含苹果公司信息的模拟结果。

**输入示例**:
当服务编排器调用 `finnhub_api.symbol_lookup("apple")`

**输出示例 (模拟)**:
```json
{
  "count": 1,
  "result": [
    {
      "description": "APPLE INC",
      "displaySymbol": "AAPL",
      "symbol": "AAPL",
      "type": "Common Stock"
    }
  ]
}
```

### 3. 获取公司新闻：`company_news` 方法

此方法用于获取指定公司在特定日期范围内的相关新闻。

```python
# 文件: src/search_server/finnhub_api.py
# ... (在 FinnhubAPI 类内部) ...
    async def company_news(self, symbol: str, from_date: str, to_date: str) -> List[Dict[str, Any]]:
        """获取指定股票在特定日期范围内的公司新闻。"""
        endpoint = f"{self.base_url}/company-news" # 公司新闻的API端点
        params = {
            "symbol": symbol,     # 公司股票代码
            "from": from_date,    # 开始日期 (格式: YYYY-MM-DD)
            "to": to_date,      # 结束日期 (格式: YYYY-MM-DD)
            "token": self.api_key # API密钥
        }
        # 同样，实际代码会使用 aiohttp 发送异步GET请求
        try:
            print(colored(f"模拟获取 {symbol} 从 {from_date} 到 {to_date} 的新闻", "cyan"))
            # 模拟成功返回一条新闻
            return [{
                "category": "company news", "datetime": 1672531200,
                "headline": f"{symbol} 公司发布了重要公告",
                "id": 12345, "image": "", "related": symbol,
                "source": "示例新闻源", "summary": "这是一个新闻摘要...",
                "url": "http://example.com/news/12345"
            }]
        except Exception as e:
            return [] # 发生错误时返回空列表
```
这个方法与 `symbol_lookup` 类似：
*   它构建了请求 Finnhub `/company-news` 端点的 URL 和参数。
*   参数包括股票代码 (`symbol`)，以及新闻的开始日期 (`from_date`) 和结束日期 (`to_date`)。
*   实际的实现会发起网络请求并返回新闻列表。我们这里用模拟数据代替。

**输入示例**:
`finnhub_api.company_news("AAPL", "2023-01-01", "2023-01-05")`

**输出示例 (模拟)**:
```json
[
  {
    "category": "company news",
    "datetime": 1672531200, // Unix时间戳
    "headline": "AAPL 公司发布了重要公告",
    "id": 12345,
    "image": "",
    "related": "AAPL",
    "source": "示例新闻源",
    "summary": "这是一个新闻摘要...",
    "url": "http://example.com/news/12345"
  }
]
```

### 4. 查找SEC备案文件：`sec_filings` 方法

此方法用于查询公司向美国证券交易委员会 (SEC) 提交的备案文件，如财务报告。

```python
# 文件: src/search_server/finnhub_api.py
# ... (在 FinnhubAPI 类内部) ...
    async def sec_filings(self, symbol: str = None, cik: str = None, ...) -> List[Dict[str, Any]]:
        """获取公司的SEC备案文件。可以按多种条件筛选。"""
        endpoint = f"{self.base_url}/stock/filings" # SEC备案文件的API端点
        params = {"token": self.api_key} # 必须包含API密钥
        
        # 根据传入的参数添加其他筛选条件
        if symbol: params["symbol"] = symbol
        if cik: params["cik"] = cik
        # ... 其他参数如 access_number, form, from_date, to_date 也可类似添加 ...

        # 实际代码会使用 aiohttp 发送异步GET请求
        try:
            print(colored(f"模拟获取 {symbol or cik} 的SEC文件", "cyan"))
            # 模拟返回一条10-K年报文件信息
            return [{
                "accessNumber": "0000320193-23-000106", "symbol": symbol or "XYZ",
                "cik": cik or "320193", "form": "10-K",
                "filedDate": "2023-10-27T16:05:00-04:00",
                "acceptedDate": "2023-10-27T16:04:59-04:00",
                "reportUrl": "https://www.sec.gov/...",
                "filingUrl": "https://www.sec.gov/..."
            }]
        except Exception as e:
            return []
```
这个方法提供了多种可选参数来筛选 SEC 文件，例如：
*   `symbol`: 公司股票代码。
*   `cik`: 公司的 CIK 号码 (SEC 的唯一标识符)。
*   `form`: 文件类型，例如 "10-K" (年报) 或 "10-Q" (季报)。
*   `from_date`, `to_date`: 日期范围。

它会构建请求到 `/stock/filings` 端点，并返回符合条件的备案文件列表。

**输入示例**:
`finnhub_api.sec_filings(symbol="AAPL", form="10-K", to_date="2023-12-31")`

**输出示例 (模拟)**:
```json
[
  {
    "accessNumber": "0000320193-23-000106",
    "symbol": "AAPL",
    "cik": "320193",
    "form": "10-K",
    "filedDate": "2023-10-27T16:05:00-04:00",
    "acceptedDate": "2023-10-27T16:04:59-04:00",
    "reportUrl": "https://www.sec.gov/Archives/edgar/data/320193/000032019323000106/aapl-20230930.htm",
    "filingUrl": "https://www.sec.gov/Archives/edgar/data/320193/000032019323000106/0000320193-23-000106-index.htm"
  }
]
```

### 与服务编排器的整合

在 `claude-mcp` 的 [服务编排器](01_服务编排器_.md) (通常在 `src/search_server/server.py` 中实现) 中，会首先创建一个 `FinnhubAPI` 类的实例：

```python
# 文件: src/search_server/server.py (概念性片段)
from .finnhub_api import FinnhubAPI # 导入FinnhubAPI类

# 在服务初始化时创建 FinnhubAPI 的实例
finnhub_client = FinnhubAPI()
```

然后，当服务编排器接收到调用特定金融工具（如 `"finnhub_symbol_lookup"`）的请求时，它的 `handle_call_tool` 函数会将请求分派给 `finnhub_client` 对象的相应方法：

```python
# 文件: src/search_server/server.py (概念性片段)
# ... (在 handle_call_tool 函数内部) ...
# elif name == "finnhub_symbol_lookup":
#     query = arguments.get("query")
#     if not query:
#         raise ValueError("参数 'query' 是必需的")
#     # 调用 finnhub_client 实例的 symbol_lookup 方法
#     raw_results = await finnhub_client.symbol_lookup(query=query)
#     # 此处可以添加对 raw_results 的进一步处理或格式化
#     return [types.TextContent(type="text", text=json.dumps(raw_results))]

# elif name == "finnhub_company_news":
#     symbol = arguments.get("symbol")
#     from_date = arguments.get("from_date")
#     to_date = arguments.get("to_date")
#     # ... 参数校验 ...
#     raw_results = await finnhub_client.company_news(
#         symbol=symbol, from_date=from_date, to_date=to_date
#     )
#     return [types.TextContent(type="text", text=json.dumps(raw_results))]

# elif name == "finnhub_sec_filings":
#     # ... 获取参数 symbol, cik, form, from_date, to_date ...
#     raw_results = await finnhub_client.sec_filings(
#         symbol=arguments.get("symbol"), 
#         cik=arguments.get("cik"),
#         # ... 传递其他参数 ...
#     )
#     return [types.TextContent(type="text", text=json.dumps(raw_results))]
```
通过这种方式，服务编排器利用 `FinnhubAPI` 实例作为其获取金融数据的“专家”，而 `FinnhubAPI` 类则负责处理与 Finnhub 服务进行实际通信的所有复杂细节。

## 总结

在本章中，我们详细了解了 `claude-mcp` 项目中的**金融数据分析助手**。我们学到了：

*   它的核心目的是**简化从 Finnhub 等专业平台获取金融数据的过程**。
*   它主要通过 `FinnhubAPI` 类实现，该类封装了与 Finnhub API 交互的逻辑。
*   主要功能包括**查询股票代码** (`symbol_lookup`)、**获取公司新闻** (`company_news`) 和**查找SEC备案文件** (`sec_filings`)。
*   使用前需要配置 `FINNHUB_API_KEY` 环境变量。
*   [服务编排器](01_服务编排器_.md) 会将这些功能作为“工具”暴露出来，并在收到相关请求时调用 `FinnhubAPI` 实例的对应方法。

金融数据分析助手为 `claude-mcp` 提供了强大的财经信息获取能力，使其能够支持更广泛的、依赖金融数据的应用场景。

在下一章中，我们将探讨另一个关键组件：[第 4 章: 知识与文档管理核心](04_知识与文档管理核心_.md)，看看它是如何帮助 `claude-mcp` 管理和利用大量信息的。

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)