[project]
name = "search_server"
version = "1.3.4"
description = "MCP Search Server"
authors = [
    { name = "<PERSON>", email = "<EMAIL>" }
]
dependencies = [
    "requests>=2.32.3",
    "trio",
    "pydantic>=2.0.0",
    "mcp>=1.0.0",
    "exceptiongroup>=1.2.2",
    "weasyprint>=64.1",
    "markdown>=3.7",
    "termcolor>=2.5.0",
    "anyio>=4.6.2.post1",
    "pandoc>=2.4",
    "finnhub-python>=2.4.22",
    "pytz>=2025.1",
    "aiohttp>=3.11.14",
    "python-dotenv-vault>=0.6.4",
    "pyjwt>=2.10.1",
    "google-auth>=2.39.0",
]
requires-python = ">=3.10"

[project.scripts]
search-server = "search_server:main"
  
[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
     
[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]
addopts = "-v --tb=short"
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::pytest.PytestDeprecationWarning",
]
               
[tool.pytest-asyncio]
default_fixture_loop_scope = "function"

[tool.pyright]
include = ["src"]
exclude = [
    "**/node_modules",
    "**/__pycache__",
    ".venv"
]
pythonVersion = "3.10"
typeCheckingMode = "off"

# Disable most warnings to reduce noise
reportMissingImports = "warning"
reportMissingModuleSource = "warning"
reportOptionalSubscript = false
reportOptionalMemberAccess = false
reportOptionalCall = false
reportOptionalIterable = false
reportOptionalContextManager = false
reportOptionalOperand = false
reportUnusedImport = false
reportUnusedVariable = false
reportUnusedFunction = false
reportUnusedClass = false
reportGeneralTypeIssues = false
reportUndefinedVariable = "error"
reportUnboundVariable = "warning"
reportAssertAlwaysTrue = false
reportSelfClsParameterName = false
reportImplicitStringConcatenation = false
reportFunctionMemberAccess = false
reportPrivateUsage = false
reportConstantRedefinition = false
reportIncompatibleMethodOverride = false
reportIncompatibleVariableOverride = false
reportOverlappingOverload = false
reportUninitializedInstanceVariable = false
reportCallInDefaultInitializer = false
reportUnnecessaryIsInstance = false
reportUnnecessaryCast = false
reportUnnecessaryComparison = false
reportUnnecessaryContains = false
reportImplicitOverride = false
reportShadowedImports = false
  
[dependency-groups]
dev = [
    "pyright>=1.1.389",
    "pytest>=8.3.3",
    "pytest-asyncio>=0.23.5",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
]
  
[tool.coverage.run]
source = ["src"]
branch = true
 
[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "pass",
    "raise ImportError",
]
ignore_errors = true
omit = [
    "tests/*",
    "setup.py",
]
   