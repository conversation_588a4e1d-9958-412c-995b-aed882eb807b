# Google OAuth PKCE Testing Guide

This guide helps you test the Google OAuth PKCE flow with your search server and <PERSON>.

## Prerequisites

- A running search server instance
- Google account for testing OAuth
- Web browser (Chrome/Firefox recommended)

## Method 1: Using the Debug Interface (Easiest)

1. **Start the search server**:
   ```bash
   uv run search-server
   ```

2. **Clear OAuth state**:
   - Visit: http://localhost:8511/auth/debug
   - Click the "Clear OAuth State" button
   - Follow instructions to revoke Google permissions if asked

3. **Test the OAuth Flow**:
   - Click the "Force Google Consent" button
   - This opens Google consent in a new tab
   - Complete the Google consent flow
   - You'll be redirected back to the server
   - Check server logs for successful token exchange

4. **Test with <PERSON>**:
   - Configure <PERSON>:
     ```bash
     python test_claude_desktop.py --configure-only
     ```
   - Launch Claude Des<PERSON>
   - Connect to the search server
   - Verify <PERSON> shows the login popup
   - Complete the authentication flow
   - Check if tools appear in <PERSON>'s interface

## Method 2: Using the clear_oauth_state.py Script

1. **Run the cleanup script**:
   ```bash
   python clear_oauth_state.py
   ```

2. **Follow the script's instructions**:
   - It will check if the server is running
   - Clear OAuth state on the server
   - Open Google account permissions page
   - Help you clear browser cookies

3. **Start Claude Desktop and test**:
   - Follow the same steps as Method 1, steps 4-5

## Method 3: Manual PKCE Testing (For Developers)

For detailed testing of PKCE verification:

1. **Run the PKCE test script**:
   ```bash
   python test_pkce_flow.py
   ```

2. **Follow the test script flow**:
   - The script generates PKCE parameters
   - Opens the authorization URL in your browser
   - Displays the code verifier and challenge for debugging
   - After successful authorization, you'll be redirected to the callback page
   - Copy the authorization code (or entire URL) from the browser
   - Paste it back into the script when prompted
   - The script will exchange the code for tokens
   - Review the logs for PKCE verification results

## Troubleshooting

### Google Consent Screen Not Appearing

1. **Clear browser cookies and cache**:
   - Focus on `accounts.google.com` and related domains
   - Or use private/incognito browsing mode

2. **Revoke app permissions in Google**:
   - Check https://myaccount.google.com/permissions AND
   - Check https://myaccount.google.com/security
   - Look for ANY apps with "Search", "Claude", or similar names

3. **Force select_account parameter**:
   - Use `/debug/force-consent` endpoint
   - This adds `prompt=consent select_account` to force the consent screen

### Tools Not Appearing in Claude

1. **Check server logs**:
   - Look for successful token exchange
   - Verify tool registration logs
   - Ensure no errors in SSE connection

2. **Try a fresh Claude Desktop start**:
   - Close Claude Desktop completely
   - Start it again and try connecting

3. **Check Claude Desktop configuration**:
   - Run `python test_claude_desktop.py --configure-only`
   - Verify the port settings match the server port

### PKCE Verification Errors

1. **Check server logs**:
   - Look for "PKCE verification" messages
   - Note any errors in code_challenge comparison

2. **Test with the manual PKCE script**:
   - The `test_pkce_flow.py` script shows the full verification process
   - Compare the calculated challenge with the expected one

3. **Try double hashing workaround**:
   - Some clients double-hash the code verifier
   - Our server already has a workaround for this

## Server Routes for Testing

- **Main debug page**: http://localhost:8511/auth/debug
- **Force consent**: http://localhost:8511/debug/force-consent
- **Clear OAuth state**: http://localhost:8511/debug/clear_oauth_state
- **Auth page**: http://localhost:8511/auth
- **Debug SSE**: http://localhost:8511/debug-sse

## Reference

The OAuth PKCE flow consists of these steps:

1. **Generate code_verifier and code_challenge**:
   - `code_verifier`: Random string between 43-128 characters
   - `code_challenge`: Base64URL(SHA256(code_verifier))

2. **Authorization Request**:
   - Send authorization request with `code_challenge` and `code_challenge_method=S256`
   - User authenticates and grants permissions
   - Authorization code returned to client

3. **Token Exchange**:
   - Exchange authorization code for tokens
   - Include original `code_verifier` in this request
   - Server verifies that hash matches the original `code_challenge`
   - If valid, tokens are issued

For more details, see [RFC 7636 - PKCE](https://tools.ietf.org/html/rfc7636)