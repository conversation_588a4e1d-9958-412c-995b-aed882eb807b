{"include": ["src"], "exclude": ["**/node_modules", "**/__pycache__", ".venv", "tests", "test_markdown", "backup", "logs"], "pythonVersion": "3.10", "typeCheckingMode": "basic", "reportMissingImports": "warning", "reportUnusedImport": false, "reportUnusedVariable": false, "reportUnusedFunction": false, "reportOptionalMemberAccess": false, "reportUndefinedVariable": "error", "reportUnboundVariable": "warning"}