{"include": ["src"], "exclude": ["**/node_modules", "**/__pycache__", ".venv", "tests", "test_markdown", "backup", "logs"], "pythonVersion": "3.10", "typeCheckingMode": "off", "reportMissingImports": "warning", "reportMissingModuleSource": "warning", "reportOptionalSubscript": false, "reportOptionalMemberAccess": false, "reportOptionalCall": false, "reportOptionalIterable": false, "reportOptionalContextManager": false, "reportOptionalOperand": false, "reportUnusedImport": false, "reportUnusedVariable": false, "reportUnusedFunction": false, "reportUnusedClass": false, "reportGeneralTypeIssues": false, "reportUndefinedVariable": "error", "reportUnboundVariable": "warning", "reportAssertAlwaysTrue": false, "reportSelfClsParameterName": false, "reportImplicitStringConcatenation": false, "reportFunctionMemberAccess": false, "reportPrivateUsage": false, "reportConstantRedefinition": false, "reportIncompatibleMethodOverride": false, "reportIncompatibleVariableOverride": false, "reportOverlappingOverload": false, "reportUninitializedInstanceVariable": false, "reportCallInDefaultInitializer": false, "reportUnnecessaryIsInstance": false, "reportUnnecessaryCast": false, "reportUnnecessaryComparison": false, "reportUnnecessaryContains": false, "reportImplicitOverride": false, "reportShadowedImports": false, "reportInvalidTypeVarUse": false}