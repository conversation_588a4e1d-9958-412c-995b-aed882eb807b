import aiohttp
import os
import time
import json
import asyncio
from typing import Optional, Dict, Any, List
from .logger import logger

class KnowledgeBase:
    def __init__(self):
        self.base_url = os.getenv("KNOWLEDGE_BASE_URL", "http://192.168.0.16:3201")
        if not self.base_url:
            logger.error("KNOWLEDGE_BASE_URL environment variable not set")
            raise ValueError("KNOWLEDGE_BASE_URL environment variable not set")
            
        self.query_url = f"{self.base_url}/query"
        logger.info("KnowledgeBase initialized successfully")

    def normalize_result(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize a single result item to ensure consistent format"""
        try:
            return {
                "file_name": str(item.get("file_name", "Unknown")).encode('utf-8').decode('utf-8'),
                "chunk_text": str(item.get("chunk_text", "")).encode('utf-8').decode('utf-8'),
                "distance": float(item.get("distance", 0.0)),
                "search_type": str(item.get("search_type", "vector")).encode('utf-8').decode('utf-8'),
                "relevance_score": float(item.get("relevance_score", 0.0))
            }
        except UnicodeError as e:
            logger.error(f"Unicode encoding error in result: {e}")
            return {
                "file_name": "Unknown",
                "chunk_text": "Error: Invalid character encoding in result",
                "distance": 0.0,
                "search_type": "vector",
                "relevance_score": 0.0
            }

    async def search(self, query: str) -> Dict[str, List[Dict[str, Any]]]:
        """
        Search documents using knowledge base API
        
        Args:
            query: The search query text
            
        Returns:
            Dict containing the search results with consistent format
        """
        try:
            # Ensure query is properly encoded
            encoded_query = query.encode('utf-8').decode('utf-8')
            
            payload = {
                "query": encoded_query,
                "k": 5,
                "llm": "claude",
                "threshold": 3,
                "full_docs_search": True,
                "rerank_method": "jina",
                "file_name": "claude-mcp-call", 
                "contextual_embedding_query": True,
                "search_engine": "bing"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(self.query_url, json=payload, timeout=30) as response:
                    if response.status != 200:
                        logger.error(f"Knowledge base search failed: HTTP {response.status}")
                        return {"results": []}
                    
                    # Decode response with explicit UTF-8 encoding
                    content = await response.text(encoding='utf-8')
                    result = json.loads(content)
                    
                    # Get raw results
                    raw_results = result.get("results", [])
                    if not isinstance(raw_results, list):
                        logger.error(f"Invalid results format: {raw_results}")
                        return {"results": []}

                    # Normalize and limit results
                    normalized_results = []
                    for item in raw_results[:5]:  # Limit to 5 results
                        try:
                            if isinstance(item, dict):
                                normalized_item = self.normalize_result(item)
                                normalized_results.append(normalized_item)
                        except (ValueError, TypeError, UnicodeError) as e:
                            logger.warning(f"Failed to normalize result item: {e}")
                            continue

                    logger.info(f"Normalized {len(normalized_results)} results")
                    return {"results": normalized_results}

        except asyncio.TimeoutError:
            logger.error("Knowledge base search timed out")
            return {"results": []}
        except aiohttp.ClientError as e:
            logger.error(f"Failed to search knowledge base: {str(e)}")
            return {"results": []}
        except UnicodeError as e:
            logger.error(f"Unicode encoding error: {str(e)}")
            return {"results": []}
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {str(e)}")
            return {"results": []}
        except Exception as e:
            logger.error(f"Unexpected error in knowledge base search: {str(e)}")
            return {"results": []} 