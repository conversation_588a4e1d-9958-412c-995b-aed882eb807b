import os
from dotenv_vault import load_dotenv
import asyncio
import time
from typing import Literal, Any, Dict, List, Optional, Set, Union
import json
import uuid
from datetime import datetime, timedelta
import pytz
import anyio
import click
import uvicorn
from termcolor import colored
from starlette.requests import Request
from starlette.responses import Response, JSONResponse

# Import FastMCP instead of traditional MCP server
from mcp.server.fastmcp import FastMCP
import mcp.types as types
from pydantic import Field

from .logger import logger

# Import refactored modules
from .server_config import (
    check_environment_variables,
    create_server
)

# Load environment variables
load_dotenv()

# Check environment variables
check_environment_variables()

# Global flag for testing without authentication
DISABLE_ALL_AUTH = os.getenv("DISABLE_ALL_AUTH", "false").lower() == "true"
# New parameter to control Google OAuth specifically
GOOGLE_OAUTH_ENABLE = os.getenv("GOOGLE_OAUTH_ENABLE", "true").lower() == "true"
# New parameter to control StreamableHTTP transport
STREAMABLE_HTTP_ENABLE = os.getenv("STREAMABLE_HTTP_ENABLE", "false").lower() == "true"

# Direct OAuth server is now the default and only implementation
# (combines direct server pattern with OAuth for Claude compatibility)

if DISABLE_ALL_AUTH:
    logger.warning(colored("********** ALL AUTHENTICATION IS DISABLED FOR TESTING **********", "red"))
    logger.warning(colored("********** DO NOT USE THIS IN PRODUCTION *****************", "red"))
    # OAuth will be disabled
    oauth_enabled = False
else:
    # Enable OAuth based on GOOGLE_OAUTH_ENABLE
    oauth_enabled = GOOGLE_OAUTH_ENABLE
    if not oauth_enabled:
        logger.warning(colored("********** GOOGLE OAUTH IS DISABLED **********", "yellow"))

# Default to None
oauth_provider = None
oauth_callback_handler = None
oauth_error_handler = None


@click.command()
@click.option("--port", default=8511, help="Port to listen on for SSE/HTTP", type=int)
@click.option(
    "--transport",
    type=click.Choice(["stdio", "sse", "streamable"]),
    default="sse",
    help="Transport type",
)
def main(port: int, transport: str) -> int:
    
    # Set environment variable for easier debugging
    os.environ["SEARCH_SERVER_PORT"] = str(port)
    os.environ["SEARCH_SERVER_TRANSPORT"] = transport
    
    if transport == "streamable":
        logger.info(colored(f"Starting StreamableHTTP server on port {port}", "green"))
        # Print a clear endpoint URL for Claude to connect to
        logger.info(colored(f"Claude should connect to: http://server_domain_name:{port}/mcp", "cyan"))
        
        # Run the server based on authentication options
        if DISABLE_ALL_AUTH:
            logger.warning(colored("********** AUTH DISABLED - RUNNING DIRECT SERVER WITHOUT AUTHENTICATION **********", "red"))
            # For now, fallback to SSE when disabling auth with StreamableHTTP
            from .direct_server import run_direct_server
            run_direct_server(port=port)
        elif GOOGLE_OAUTH_ENABLE:
            # Use the StreamableHTTP server with Google OAuth
            logger.info(colored("********** RUNNING STREAMABLE HTTP SERVER WITH GOOGLE OAUTH **********", "green"))
            from .google_oauth_stream_server import run_google_oauth_stream_server
            run_google_oauth_stream_server(port=port)
        else:
            # For now, fallback to direct OAuth server without StreamableHTTP
            logger.info(colored("********** RUNNING DIRECT OAUTH SERVER WITHOUT GOOGLE OAUTH (FALLBACK) **********", "yellow"))
            from .direct_oauth_server import run_direct_oauth_server  
            run_direct_oauth_server(port=port)
    elif transport == "sse":
        logger.info(colored(f"Starting SSE server on port {port}", "green"))
        # Print a clear endpoint URL for Claude to connect to
        logger.info(colored(f"Claude should connect to: http://server_domain_name:{port}/sse", "cyan"))
        
        # Run the wrapped app with reduced logging from uvicorn
        logger.info(colored(f"Starting SSE server with Claude direct middleware on port {port}", "green"))
        
        # Add CORS headers to help with client connectivity
        # Show the environment variables status for testing
        if DISABLE_ALL_AUTH:
            logger.warning(colored("********** AUTH DISABLED - RUNNING DIRECT SERVER WITHOUT AUTHENTICATION **********", "red"))
            # Run the direct server implementation that doesn't use OAuth
            from .direct_server import run_direct_server
            run_direct_server(port=port)
        elif GOOGLE_OAUTH_ENABLE:
            # Use the direct OAuth server implementation (direct pattern + OAuth)
            # This is the recommended implementation for Claude + OAuth integration
            logger.info(colored("********** RUNNING DIRECT OAUTH SERVER WITH GOOGLE OAUTH **********", "green"))
            from .google_oauth_server import run_google_oauth_server
            run_google_oauth_server(port=port)
        else:
            # Use direct OAuth server but without Google OAuth
            logger.info(colored("********** RUNNING DIRECT OAUTH SERVER WITHOUT GOOGLE OAUTH **********", "yellow"))
            from .direct_oauth_server import run_direct_oauth_server  
            run_direct_oauth_server(port=port)
    else:
        # Handle stdio transport
        logger.warning(colored("********** AUTH DISABLED - RUNNING DIRECT SERVER WITHOUT AUTHENTICATION **********", "red"))
        # Run the direct server implementation for stdio
        from .direct_server import run_stdio_direct_server
        import anyio
        anyio.run(run_stdio_direct_server)
    return 0

if __name__ == "__main__":
    main()