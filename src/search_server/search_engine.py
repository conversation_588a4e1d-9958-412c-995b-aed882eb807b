import aiohttp
import json
import os
import asyncio
from typing import List, Dict, Any
from .utils.logging_utils import setup_component_logger

# Set up component-specific logger
logger = setup_component_logger("search_engine", enable_rotation=True)

class SearchEngine:
    def __init__(self):
        self.tavily_api_key = os.getenv("TAVILY_API_KEY")
        self.serper_api_key = os.getenv("SERPER_API_KEY")
        self.bing_api_key = os.getenv("BING_API_KEY")
        self.google_api_key = os.getenv("GOOGLE_API_KEY")
        self.google_search_engine_id = os.getenv("GOOGLE_SEARCH_ENGINE_ID")
        self.linkup_api_key = os.getenv("LINKUP_API_KEY")
        self.exa_api_key = os.getenv("EXA_API_KEY")
        self.txyz_api_key = os.getenv("TXYZ_API_KEY")
        self.firecrawl_api_key = os.getenv("FIRECRAWL_API_KEY")
        logger.info("SearchEngine initialized successfully")
        
    async def search_tavily(self, query: str) -> List[Dict[str, Any]]:
        endpoint = "https://api.tavily.com/search"
        payload = {
            "query": query,
            "topic": "general",
            "search_depth": "advanced",
            "max_results": 10,
            "include_answer": False,
            "include_raw_content": False,
            "include_images": False,
        }
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.tavily_api_key}"
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(endpoint, headers=headers, json=payload, timeout=30) as response:
                    if response.status != 200:
                        logger.error(f"Error in Tavily search: HTTP {response.status}")
                        return []
                    
                    search_results = await response.json()
                    
                    results = []
                    for result in search_results.get('results', []):
                        results.append({
                            "file_name": "Tavily",
                            "chunk_text": result.get('content'),
                            "distance": 0.9,
                            "search_type": "web",
                            "url": result.get('url')
                        })
                    return results
        except asyncio.TimeoutError:
            logger.error(f"Timeout in Tavily search for query: {query}")
            return []
        except Exception as e:
            logger.error(f"Error in Tavily search: {e}")
            return []

    async def search_serper(self, query: str) -> List[Dict[str, Any]]:
        url = "https://google.serper.dev/search"
        payload = json.dumps({
            "q": query,
            "hl": "zh-cn",
            "num": 10
        })
        headers = {
            'X-API-KEY': self.serper_api_key,
            'Content-Type': 'application/json'
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, data=payload, timeout=30) as response:
                    if response.status != 200:
                        logger.error(f"Error in Serper search: HTTP {response.status}")
                        return []
                    
                    search_results = await response.json()

                    results = []
                    for result in search_results.get('organic', []):
                        results.append({
                            "file_name": "Serper",
                            "chunk_text": result.get('snippet'),
                            "distance": 0.9,
                            "search_type": "web",
                            "url": result.get('link')
                        })
                    return results
        except asyncio.TimeoutError:
            logger.error(f"Timeout in Serper search for query: {query}")
            return []
        except Exception as e:
            logger.error(f"Error in Serper search: {e}")
            return []

    async def search_bing(self, query: str) -> List[Dict[str, Any]]:
        endpoint = "https://api.bing.microsoft.com/v7.0/search"
        headers = {"Ocp-Apim-Subscription-Key": self.bing_api_key}
        params = {"q": query, "mkt": "global"}

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(endpoint, headers=headers, params=params, timeout=30) as response:
                    if response.status != 200:
                        logger.error(f"Error in Bing search: HTTP {response.status}")
                        return []
                    
                    search_results = await response.json()

                    results = []
                    for result in search_results.get('webPages', {}).get('value', []):
                        results.append({
                            "file_name": "Bing",
                            "chunk_text": result.get('snippet'),
                            "distance": 0.9,
                            "search_type": "web",
                            "url": result.get('displayUrl')
                        })
                    return results
        except asyncio.TimeoutError:
            logger.error(f"Timeout in Bing search for query: {query}")
            return []
        except Exception as e:
            logger.error(f"Error in Bing search: {e}")
            return []

    async def search_google(self, query: str) -> List[Dict[str, Any]]:
        url = "https://www.googleapis.com/customsearch/v1"
        params = {
            "key": self.google_api_key,
            "cx": self.google_search_engine_id,
            "q": query
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, timeout=30) as response:
                    if response.status != 200:
                        logger.error(f"Error in Google search: HTTP {response.status}")
                        return []
                    
                    search_results = await response.json()

                    results = []
                    for item in search_results.get('items', []):
                        results.append({
                            "file_name": "Google",
                            "chunk_text": item.get('snippet'),
                            "distance": 0.9,
                            "search_type": "web",
                            "url": item.get('link')
                        })
                    return results
        except asyncio.TimeoutError:
            logger.error(f"Timeout in Google search for query: {query}")
            return []
        except Exception as e:
            logger.error(f"Error in Google search: {e}")
            return []

    async def search_linkup(self, query: str) -> List[Dict[str, Any]]:
        endpoint = "https://api.linkup.so/v1/search"
        params = {
            "q": query,
            "depth": "standard",
            "outputType": "searchResults"
        }
        headers = {
            "Authorization": f"Bearer {self.linkup_api_key}"
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(endpoint, headers=headers, params=params, timeout=30) as response:
                    if response.status != 200:
                        logger.error(f"Error in Linkup search: HTTP {response.status}")
                        return []
                    
                    search_results = await response.json()

                    results = []
                    for result in search_results.get('results', []):
                        results.append({
                            "file_name": "Linkup",
                            "chunk_text": result.get('content'),
                            "distance": 0.9,
                            "search_type": "web",
                            "url": result.get('url')
                        })
                    return results
        except asyncio.TimeoutError:
            logger.error(f"Timeout in Linkup search for query: {query}")
            return []
        except Exception as e:
            logger.error(f"Error in Linkup search: {e}")
            return []

    async def search_exa(self, query: str) -> List[Dict[str, Any]]:
        endpoint = "https://api.exa.ai/search"
        headers = {
            "accept": "application/json",
            "content-type": "application/json",
            "x-api-key": self.exa_api_key
        }
        data = {
            "query": query,
            "type": "auto",
            "useAutoprompt": True,
            "numResults": 10,
            "contents": {
                "summary": True
            }
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(endpoint, headers=headers, json=data, timeout=30) as response:
                    if response.status != 200:
                        logger.error(f"Error in Exa search: HTTP {response.status}")
                        return []
                    
                    search_results = await response.json()

                    results = []
                    for result in search_results.get('results', []):
                        results.append({
                            "file_name": "Exa",
                            "chunk_text": result.get('summary'),
                            "distance": 0.9,
                            "search_type": "web",
                            "url": result.get('url')
                        })
                    return results
        except asyncio.TimeoutError:
            logger.error(f"Timeout in Exa search for query: {query}")
            return []
        except Exception as e:
            logger.error(f"Error in Exa search: {e}")
            return []

    async def search_txyz(self, query: str) -> List[Dict[str, Any]]:
        endpoint = "https://api.txyz.ai/v1/search/smart"
        headers = {
            "Authorization": f"Bearer {self.txyz_api_key}",
            "Content-Type": "text/plain;charset=UTF-8"
        }
        params = {
            "query": query,
            "max_num_results": 10
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(endpoint, headers=headers, params=params, timeout=30) as response:
                    if response.status != 200:
                        logger.error(f"Error in TXYZ search: HTTP {response.status}")
                        return []
                    
                    search_results = await response.json()

                    results = []
                    for result in search_results.get('results', []):
                        results.append({
                            "file_name": result.get('title', 'TXYZ'),
                            "chunk_text": result.get('snippet'),
                            "distance": result.get('number_of_citations', 0),
                            "search_type": "web",
                            "url": result.get('link')
                        })
                    return results
        except asyncio.TimeoutError:
            logger.error(f"Timeout in TXYZ search for query: {query}")
            return []
        except Exception as e:
            logger.error(f"Error in TXYZ search: {e}")
            return []

    async def search_firecrawl(self, query: str) -> List[Dict[str, Any]]:
        endpoint = "https://api.firecrawl.dev/v1/deep-research"
        headers = {
            "Authorization": f"Bearer {self.firecrawl_api_key}",
            "Content-Type": "application/json"
        }
        data = {
            "query": query,
            "maxDepth": 7,
            "timeLimit": 200,
            "maxUrls": 15
        }

        try:
            # Submit the search job
            async with aiohttp.ClientSession() as session:
                async with session.post(endpoint, headers=headers, json=data, timeout=200) as response:
                    if response.status != 200:
                        logger.error(f"Error in Firecrawl search submission: HTTP {response.status}")
                        return []
                    
                    job_response = await response.json()
                    
                    if not job_response.get('success'):
                        logger.error("Firecrawl job submission failed")
                        return []

                    job_id = job_response.get('id')
                    if not job_id:
                        logger.error("No job ID received from Firecrawl")
                        return []

                    # Poll for results
                    status_endpoint = f"{endpoint}/{job_id}"
                    max_retries = 10  # Maximum number of polling attempts
                    retry_delay = 20    # Delay between polls in seconds

                    for attempt in range(max_retries):
                        async with session.get(status_endpoint, headers=headers) as status_response:
                            if status_response.status != 200:
                                logger.error(f"Error checking Firecrawl job status: HTTP {status_response.status}")
                                return []

                            status_data = await status_response.json()
                            
                            # Regular status logging
                            logger.info("=" * 50)
                            logger.info("FIRECRAWL SEARCH STATUS")
                            logger.info("=" * 50)
                            logger.info(f"Query: {query}")
                            logger.info(f"Job ID: {job_id}")
                            logger.info(f"Attempt: {attempt + 1}/{max_retries}")
                            logger.info("-" * 30)
                            
                            # Log activities if available
                            activities = status_data.get('activities', [])
                            if activities:
                                logger.info("Recent Activities:")
                                for activity in activities[-3:]:  # Show last 3 activities
                                    logger.info(f"  • [{activity.get('timestamp')}] {activity.get('type').upper()}")
                                    logger.info(f"    Message: {activity.get('message')}")
                                    logger.info(f"    Depth: {activity.get('depth')}")
                                    logger.info(f"    Status: {activity.get('status')}")
                            logger.info("=" * 50)
                            
                            if not status_data.get('success'):
                                logger.error("Firecrawl status check failed")
                                return []

                            # Check completion status - only need to check top level status
                            if status_data.get('status') == "completed":
                                logger.info("Job completed successfully!")
                                
                                # Process completed results
                                results = []
                                
                                # Add sources
                                sources = status_data.get('sources', [])
                                for source in sources:
                                    chunk_text = f"[{source.get('title', '')}] {source.get('description', '')}"
                                    results.append({
                                        "file_name": "Firecrawl",
                                        "chunk_text": chunk_text,
                                        "distance": 0.9,
                                        "search_type": "web",
                                        "url": source.get('url')
                                    })
                                
                                # Add final analysis as an additional result if available
                                final_analysis = status_data.get('data', {}).get('finalAnalysis')
                                if final_analysis:
                                    results.append({
                                        "file_name": "Firecrawl Analysis",
                                        "chunk_text": final_analysis,
                                        "distance": 0.9,
                                        "search_type": "web",
                                        "url": "firecrawl://analysis"
                                    })
                                
                                return results
                            
                            # Wait before next poll
                            await asyncio.sleep(retry_delay)
                    
                    logger.error("Firecrawl job timed out")
                    return []

        except asyncio.TimeoutError:
            logger.error(f"Timeout in Firecrawl search for query: {query}")
            return []
        except Exception as e:
            logger.error(f"Error in Firecrawl search: {e}")
            return []

    async def search(self, engine: str, query: str) -> List[Dict[str, Any]]:
        if engine == "tavily":
            return await self.search_tavily(query)
        elif engine == "serper":
            return await self.search_serper(query)
        elif engine == "bing":
            return await self.search_bing(query)
        elif engine == "google":
            return await self.search_google(query)
        elif engine == "linkup":
            return await self.search_linkup(query)
        elif engine == "exa":
            return await self.search_exa(query)
        elif engine == "txyz":
            return await self.search_txyz(query)
        elif engine == "firecrawl":
            return await self.search_firecrawl(query)
        else:
            return [] 