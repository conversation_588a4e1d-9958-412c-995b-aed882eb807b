"""
Common tool definitions used across different server implementations.

This module provides consistent tool definitions to avoid duplication 
across server implementations. It contains the input schemas and tool
descriptions for all tools exposed by the search server.
"""
import mcp.types as types

def get_tool_definitions() -> list[types.Tool]:
    """Return a list of all tool definitions used by search server.
    
    These definitions are shared across all server implementations
    to ensure consistency and reduce duplication.
    """
    return [
        types.Tool(
            name="search",
            description="""Search internet using specified search engine:
            - tavily is the good choice for general research
            - serper is the good choice for general research
            - bing is the good choice for general research
            - google is the good choice for general research
            - linkup is the good choice for deep research
            - exa is the good choice for deep research
            - txyz is the best choice for article research
            - firecrawl is the best choice for deep research but slow
            """,
            inputSchema={
                "type": "object",
                "properties": {
                    "engine": {
                        "type": "string",
                        "enum": ["tavily", "serper", "bing", "google", "linkup", "exa", "txyz", "firecrawl"],
                        "description": "Search engine to use (tavily, serper, bing, google, linkup, exa, txyz, firecrawl) and firecrawl is the best choice for deep research but slowest"
                    },
                    "query": {
                        "type": "string",
                        "description": "Search query"
                    }
                },
                "required": ["engine", "query"],
            },
        ),
        types.Tool(
            name="scrape_url",
            description="The best choice for scraping content from a URL and return it in markdown format",
            inputSchema={
                "type": "object",
                "properties": {
                    "url": {
                        "type": "string",
                        "description": "URL to scrape"
                    }
                },
                "required": ["url"],
            },
        ),
        types.Tool(
            name="knowledge_search",
            description="Search my knowledge base",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Search query for knowledge base"
                    }
                },
                "required": ["query"],
            },
        ),
        types.Tool(
            name="markdown_to_pdf",
            description="Convert text (usually markdown format) to PDF file",
            inputSchema={
                "type": "object",
                "properties": {
                    "title": {
                        "type": "string",
                        "description": "Title to appear in the head of PDF report"
                    },
                    "content": {
                        "type": "string",
                        "description": "Content of text file (markdown format)"
                    },
                    "filename": {
                        "type": "string",
                        "description": "Name of PDF file (should be related to the content and end with '.pdf')"
                    },
                    "use_pandoc": {
                        "type": "boolean",
                        "description": "Default is true, if failure, we set it to false",
                    }
                },
                "required": ["title", "content", "filename", "use_pandoc"],
            },
        ),
        # Financial tools using Finnhub API
        types.Tool(
            name="finnhub_symbol_lookup",
            description="Search for best-matching stock symbols based on your query",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Search term (company name, symbol, ISIN, Cusip, etc.)"
                    }
                },
                "required": ["query"],
            },
        ),
        types.Tool(
            name="finnhub_company_news",
            description="List latest company news by symbol (North American companies only)",
            inputSchema={
                "type": "object",
                "properties": {
                    "symbol": {
                        "type": "string",
                        "description": "Company symbol (e.g., AAPL)"
                    },
                    "from_date": {
                        "type": "string",
                        "description": "Start date in format YYYY-MM-DD"
                    },
                    "to_date": {
                        "type": "string",
                        "description": "End date in format YYYY-MM-DD"
                    }
                },
                "required": ["symbol", "from_date", "to_date"],
            },
        ),
        types.Tool(
            name="finnhub_sec_filings",
            description="List company's SEC filings",
            inputSchema={
                "type": "object",
                "properties": {
                    "symbol": {
                        "type": "string",
                        "description": "Company symbol (e.g., AAPL)"
                    },
                    "cik": {
                        "type": "string",
                        "description": "CIK number (optional)"
                    },
                    "access_number": {
                        "type": "string",
                        "description": "Access number of a specific report (optional)"
                    },
                    "form": {
                        "type": "string",
                        "description": "Filter by form type, e.g., '10-K' (optional)"
                    },
                    "from_date": {
                        "type": "string",
                        "description": "Start date in format YYYY-MM-DD (optional)"
                    },
                    "to_date": {
                        "type": "string",
                        "description": "End date in format YYYY-MM-DD (optional)"
                    }
                },
                "required": ["symbol"],
            },
        ),
        # GroundX API tools
        types.Tool(
            name="groundx_get_all_buckets",
            description="List all buckets within your GroundX account",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": [],
            },
        ),
        types.Tool(
            name="groundx_get_all_documents",
            description="List all documents across all resources which are currently on GroundX",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": [],
            },
        ),
        types.Tool(
            name="groundx_get_bucket",
            description="Look up a specific bucket by its bucketId within your GroundX account",
            inputSchema={
                "type": "object",
                "properties": {
                    "bucket_id": {
                        "type": "integer",
                        "description": "ID of the bucket to retrieve"
                    }
                },
                "required": ["bucket_id"],
            },
        ),
        types.Tool(
            name="groundx_create_bucket",
            description="Create a new bucket in your GroundX account",
            inputSchema={
                "type": "object",
                "properties": {
                    "name": {
                        "type": "string",
                        "description": "Name for the new bucket"
                    }
                },
                "required": ["name"],
            },
        ),
        types.Tool(
            name="groundx_delete_bucket",
            description="Delete a bucket from your GroundX account",
            inputSchema={
                "type": "object",
                "properties": {
                    "bucket_id": {
                        "type": "integer",
                        "description": "ID of the bucket to delete"
                    }
                },
                "required": ["bucket_id"],
            },
        ),
        types.Tool(
            name="groundx_search_documents",
            description="Search documents on GroundX for the most relevant information",
            inputSchema={
                "type": "object",
                "properties": {
                    "bucket_id": {
                        "type": "integer",
                        "description": "ID of the bucket to search in"
                    },
                    "query": {
                        "type": "string",
                        "description": "Search query string"
                    },
                    "relevance": {
                        "type": "integer",
                        "description": "Number of relevant results to return (default: 10)",
                        "default": 10
                    }
                },
                "required": ["bucket_id", "query"],
            },
        ),
        types.Tool(
            name="groundx_upload_documents",
            description="Upload documents to a GroundX bucket",
            inputSchema={
                "type": "object",
                "properties": {
                    "bucket_id": {
                        "type": "integer",
                        "description": "ID of the bucket to upload to"
                    },
                    "files": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "file_path": {
                                    "type": "string",
                                    "description": "Path to the file to upload"
                                },
                                "fileName": {
                                    "type": "string",
                                    "description": "Name of the file"
                                },
                                "fileType": {
                                    "type": "string",
                                    "description": "Type of file (e.g., 'pdf', 'txt')"
                                }
                            },
                            "required": ["file_path", "fileName", "fileType"]
                        },
                        "description": "List of files to upload"
                    }
                },
                "required": ["bucket_id", "files"],
            },
        ),
        # Current date and time tool
        types.Tool(
            name="get_datetime",
            description="Get current date and time",
            inputSchema={
                "type": "object",
                "properties": {
                    "timezone": {
                        "type": "string",
                        "description": "Timezone to use (e.g., 'UTC', 'US/Eastern', 'Europe/London', etc.). Defaults to UTC if not specified."
                    },
                    "format": {
                        "type": "string",
                        "description": "Output format (e.g., 'iso', 'full', 'date', 'time'). Defaults to 'iso' if not specified."
                    }
                },
                "required": [],
            },
        ),
        # Thinking tool for complex reasoning
        types.Tool(
            name="review_thought",
            description="""
            Call the tool when you need to review your thought/plan/strategy due to:
            1. something new information you get 
            2. something get failure or not working 
            3. before you provide summary to a question. 
            4. complex reasoning or some cache memory is needed
            It will not obtain new information or change the database, but just log the thought. 
            you can use it to think about a new way or improve the current way until you find a better solution.
            """,
            inputSchema={
                "type": "object",
                "properties": {
                    "thought": {
                        "type": "string",
                        "description": "A thought and some related information to think about."
                    }
                },
                "required": ["thought"],
            },
        )
    ]