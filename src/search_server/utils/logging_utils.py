import logging
import os
from datetime import datetime
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler

def setup_component_logger(component_name, log_level=logging.INFO, enable_rotation=True):
    """
    Set up a logger for a specific component that writes to the logs directory.
    
    Args:
        component_name: Name of the component/module
        log_level: Logging level (default: INFO)
        enable_rotation: Whether to use rotating file handlers (default: True)
        
    Returns:
        A configured logger instance
    """
    # Get the root project directory (3 levels up from this file)
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    logs_dir = os.path.join(project_root, "logs")
    
    # Create logs directory if it doesn't exist
    os.makedirs(logs_dir, exist_ok=True)
    
    # Create a logger with the component name
    logger = logging.getLogger(f"search-server.{component_name}")
    logger.setLevel(log_level)
    
    # Clear any existing handlers to prevent duplicates
    if logger.handlers:
        logger.handlers.clear()
    
    # Prevent propagation to root logger
    logger.propagate = False
    
    # Define log format
    log_format = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # Set up file handler
    log_file = os.path.join(logs_dir, f"{component_name}.log")
    
    if enable_rotation:
        # Use rotating file handler to prevent logs from growing too large
        file_handler = RotatingFileHandler(
            log_file, 
            maxBytes=10 * 1024 * 1024,  # 10 MB
            backupCount=5
        )
    else:
        # Regular file handler
        file_handler = logging.FileHandler(log_file)
    
    file_handler.setFormatter(log_format)
    file_handler.setLevel(log_level)
    logger.addHandler(file_handler)
    
    return logger

def setup_daily_rotating_logger(component_name, log_level=logging.INFO):
    """
    Set up a logger with daily rotation for logs that are date-sensitive.
    
    Args:
        component_name: Name of the component/module
        log_level: Logging level (default: INFO)
        
    Returns:
        A configured logger instance with daily rotation
    """
    # Get the root project directory (3 levels up from this file)
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    logs_dir = os.path.join(project_root, "logs")
    
    # Create logs directory if it doesn't exist
    os.makedirs(logs_dir, exist_ok=True)
    
    # Create a logger with the component name
    logger = logging.getLogger(f"search-server.{component_name}")
    logger.setLevel(log_level)
    
    # Clear any existing handlers to prevent duplicates
    if logger.handlers:
        logger.handlers.clear()
    
    # Prevent propagation to root logger
    logger.propagate = False
    
    # Define log format
    log_format = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # Set up timed rotating file handler
    log_file = os.path.join(logs_dir, f"{component_name}.log")
    file_handler = TimedRotatingFileHandler(
        log_file,
        when="midnight",
        interval=1,  # Rotate every day
        backupCount=14  # Keep 14 days of logs
    )
    
    file_handler.setFormatter(log_format)
    file_handler.setLevel(log_level)
    logger.addHandler(file_handler)
    
    return logger