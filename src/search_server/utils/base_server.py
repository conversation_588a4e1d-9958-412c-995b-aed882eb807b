"""
Base server class for search server implementations.

This module provides a base server class with shared functionality
that can be inherited by different server implementations.
"""
import os
import asyncio
import time
import json
from typing import Any, Dict, List, Optional, Tuple

from mcp.server import Server, NotificationOptions
from mcp.server.models import InitializationOptions
from mcp.server.sse import SseServerTransport
import mcp.types as types
import uvicorn
from termcolor import colored

from ..logger import logger
from ..search_engine import SearchEngine
from ..web_scraper import WebScraper
from ..knowledge_base import KnowledgeBase
from ..markdown_to_pdf import MarkdownToPdfTool
from ..finnhub_api import <PERSON>hubAP<PERSON>
from ..groundx_api import GroundXAPI

from .tools import get_tool_definitions
from .handlers import (
    handle_call_tool, 
    handle_list_resources, 
    handle_read_resource,
    set_logging_level,
    handle_list_prompts,
    handle_get_prompt
)

# Constants
PANDOC_AVAILABLE = False
try:
    from ..pandoc_markdown_to_pdf import PandocMarkdownToPdfTool
    PANDOC_AVAILABLE = True
except ImportError:
    PANDOC_AVAILABLE = False

class BaseSearchServer:
    """Base class for search server implementations."""
    
    def __init__(self, server_name="search_server"):
        """Initialize common components and server."""
        self.server_name = server_name
        self.server = Server(server_name)
        
        # Load shared components
        self._initialize_components()
        
        # Register common handlers
        self._register_handlers()
        
    def _initialize_components(self):
        """Initialize common components used by all server implementations."""
        try:
            self.search_engine = SearchEngine()
            self.web_scraper = WebScraper()
            self.knowledge_base = KnowledgeBase()
            self.finnhub_api = FinnhubAPI()
            self.groundx_api = GroundXAPI()
            
            self.default_pdf_tool = MarkdownToPdfTool()
            self.pandoc_pdf_tool = None
            
            if PANDOC_AVAILABLE:
                try:
                    self.pandoc_pdf_tool = PandocMarkdownToPdfTool()
                    logger.info("Pandoc PDF conversion available")
                except Exception as e:
                    logger.warning(f"Failed to initialize Pandoc PDF tool: {str(e)}")
            else:
                logger.info("Pandoc not available, only default PDF conversion will be used")
                
            logger.info("All components initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize components: {str(e)}")
            raise
            
    def _register_handlers(self):
        """Register common handlers with the server."""
        @self.server.set_logging_level()
        async def handle_set_logging_level(level: types.LoggingLevel) -> None:
            await set_logging_level(self.server, level)
        
        @self.server.list_tools()
        async def handle_list_tools() -> list[types.Tool]:
            """List available tools."""
            logger.info(colored(f"{self.server_name.upper()}: Listing available tools", "green"))
            return get_tool_definitions()
        
        @self.server.call_tool()
        async def handle_tool_call(
            name: str, arguments: dict | None
        ) -> list[types.TextContent | types.ImageContent | types.EmbeddedResource]:
            """Handle tool execution requests."""
            return await handle_call_tool(
                name=name,
                arguments=arguments,
                search_engine=self.search_engine,
                web_scraper=self.web_scraper,
                knowledge_base=self.knowledge_base,
                default_pdf_tool=self.default_pdf_tool,
                pandoc_pdf_tool=self.pandoc_pdf_tool,
                finnhub_api=self.finnhub_api,
                groundx_api=self.groundx_api,
                server_context=self.server
            )
        
        @self.server.list_resources()
        async def resources_handler() -> list[types.Resource]:
            return await handle_list_resources()
        
        @self.server.read_resource()
        async def resource_read_handler(uri: types.AnyUrl) -> str:
            return await handle_read_resource(uri)
            
        # Prompt handling
        @self.server.list_prompts()
        async def prompts_handler() -> list[types.Prompt]:
            logger.info(colored(f"{self.server_name.upper()}: Listing available prompts", "green"))
            return await handle_list_prompts()
            
        @self.server.get_prompt()
        async def prompt_handler(name: str, arguments: dict[str, str] | None = None) -> types.GetPromptResult:
            logger.info(colored(f"{self.server_name.upper()}: Getting prompt: {name}", "green"))
            return await handle_get_prompt(name, arguments)
            
    def create_sse_app(self, port=8511):
        """Create an ASGI application for handling SSE and message endpoints."""
        sse = SseServerTransport("/messages")
        
        async def app(scope, receive, send):
            """ASGI application for handling SSE and message endpoints."""
            if scope["type"] != "http":
                return
            
            path = scope["path"]
            method = scope["method"]
            
            # Add CORS headers
            cors_headers = [
                (b"access-control-allow-origin", b"*"),
                (b"access-control-allow-headers", b"*"),
                (b"access-control-allow-methods", b"GET, POST, OPTIONS"),
            ]
            
            # Handle OPTIONS preflight requests
            if method == "OPTIONS":
                logger.info(colored(f"{self.server_name.upper()}: Handling OPTIONS request for {path}", "cyan"))
                await send({
                    "type": "http.response.start",
                    "status": 200,
                    "headers": cors_headers,
                })
                await send({
                    "type": "http.response.body",
                    "body": b"",
                })
                return
            
            # Handle SSE endpoint
            if path == "/sse" and method == "GET":
                logger.info(colored(f"{self.server_name.upper()}: SSE connection requested", "green"))
                
                # Implement custom authentication logic in subclasses
                if not self.authenticate_request(scope):
                    logger.error(colored(f"{self.server_name.upper()}: Authentication failed", "red"))
                    await send({
                        "type": "http.response.start",
                        "status": 401,
                        "headers": cors_headers + [(b"content-type", b"text/plain")],
                    })
                    await send({
                        "type": "http.response.body",
                        "body": b"Authentication required",
                    })
                    return
                
                # Use connect_sse to establish SSE connection
                async with sse.connect_sse(scope, receive, send) as streams:
                    logger.info(colored(f"{self.server_name.upper()}: SSE connection established", "green"))
                    try:
                        await self.server.run(
                            streams[0],
                            streams[1],
                            InitializationOptions(
                                server_name=self.server_name,
                                server_version="1.3.4",
                                capabilities=self.server.get_capabilities(
                                    notification_options=NotificationOptions(),
                                    experimental_capabilities={},
                                ),
                            ),
                        )
                    except Exception as e:
                        logger.error(colored(f"{self.server_name.upper()}: Error in SSE connection: {str(e)}", "red"))
                        raise
                    finally:
                        logger.info(colored(f"{self.server_name.upper()}: SSE connection closed", "yellow"))
            
            # Handle SSE message endpoint
            elif path == "/messages" and method == "POST":
                logger.info(colored(f"{self.server_name.upper()}: Message received", "green"))
                try:
                    # Let SSE transport handle the message
                    await sse.handle_post_message(scope, receive, send)
                except Exception as e:
                    logger.error(colored(f"{self.server_name.upper()}: Error handling message: {str(e)}", "red"))
                    # Send error response
                    await send({
                        "type": "http.response.start",
                        "status": 500,
                        "headers": cors_headers + [(b"content-type", b"text/plain")],
                    })
                    await send({
                        "type": "http.response.body",
                        "body": str(e).encode(),
                    })
            
            # Handle healthcheck endpoint
            elif path == "/healthcheck":
                logger.info(colored(f"{self.server_name.upper()}: Healthcheck requested", "green"))
                health_data = await self.get_health_data()
                
                await send({
                    "type": "http.response.start",
                    "status": 200,
                    "headers": cors_headers + [(b"content-type", b"application/json")],
                })
                await send({
                    "type": "http.response.body",
                    "body": json.dumps(health_data).encode(),
                })
            
            # Handle custom server-specific endpoints in subclasses
            elif await self.handle_custom_endpoint(path, method, scope, receive, send, cors_headers):
                # If the subclass handled the endpoint, we're done
                return
            
            # Return 404 for unknown paths
            else:
                logger.warning(colored(f"{self.server_name.upper()}: Unknown path: {path}", "yellow"))
                await send({
                    "type": "http.response.start",
                    "status": 404,
                    "headers": cors_headers + [(b"content-type", b"text/plain")],
                })
                await send({
                    "type": "http.response.body",
                    "body": f"Not Found: {path}".encode(),
                })
                
        return app, sse
    
    def authenticate_request(self, scope):
        """Authenticate incoming requests.
        
        Override this method in subclasses to implement authentication.
        By default, it allows all requests if DISABLE_ALL_AUTH=true.
        """
        return os.getenv("DISABLE_ALL_AUTH", "false").lower() == "true"
    
    async def handle_custom_endpoint(self, path, method, scope, receive, send, cors_headers):
        """Handle custom server-specific endpoints.
        
        Override this method in subclasses to implement custom endpoints.
        Return True if the endpoint was handled, False otherwise.
        """
        return False
    
    async def get_health_data(self):
        """Get server health data for healthcheck endpoint.
        
        Override this method in subclasses to add custom health data.
        """
        return {
            "status": "ok",
            "server": self.server_name,
            "time": str(time.time()),
            "tools_count": len(get_tool_definitions())
        }
    
    def run_server(self, port=8511):
        """Run the server on the specified port."""
        app, _ = self.create_sse_app(port)
        
        # Run the ASGI application with uvicorn
        logger.info(colored(f"{self.server_name.upper()}: Starting on port {port}", "green"))
        uvicorn.run(app, host="0.0.0.0", port=port, log_level="warning")