"""
Common handler functions used across different server implementations.

This module provides shared handler implementations for tools and resources
to avoid duplication across server implementations.
"""
import os
import time
from typing import Any, Dict, List, Optional
import pytz
from datetime import datetime

import mcp.types as types
from pydantic import AnyUrl

from ..logger import logger
from termcolor import colored

# Dictionary to store notes (used in resources)
notes: dict[str, str] = {}

async def handle_call_tool(
    name: str, 
    arguments: dict | None,
    search_engine: Any, 
    web_scraper: Any, 
    knowledge_base: Any,
    default_pdf_tool: Any,
    pandoc_pdf_tool: Optional[Any],
    finnhub_api: Any,
    groundx_api: Any,
    server_context: Any
) -> list[types.TextContent | types.ImageContent | types.EmbeddedResource]:
    """
    Shared handler for tool execution requests.
    This function is used by all server implementations.
    """
    try:
        # Knowledge search tool
        if name == "knowledge_search":
            query = arguments.get("query")
            if not query:
                logger.error("Missing query for knowledge search")
                return [
                    types.TextContent(
                        type="text",
                        text="Missing query parameter"
                    )
                ]

            logger.info(f"Searching knowledge base: {query}")
            try:
                result = await knowledge_base.search(query)
                
                # Ensure we have a valid result structure
                if not isinstance(result, dict):
                    logger.error(f"Invalid response type: {type(result)}")
                    return [
                        types.TextContent(
                            type="text",
                            text="Invalid response from knowledge base"
                        )
                    ]

                # Get results array, defaulting to empty list if not present
                results = result.get("results", [])
                if not isinstance(results, list):
                    logger.error(f"Invalid results type: {type(results)}")
                    return [
                        types.TextContent(
                            type="text",
                            text="Invalid results format from knowledge base"
                        )
                    ]

                # Format results
                formatted_results = []
                for item in results:
                    try:
                        if not isinstance(item, dict):
                            continue
                            
                        formatted_results.append(
                            types.TextContent(
                                type="text",
                                text=(
                                    f"Source: {str(item.get('file_name', 'Unknown'))}\n"
                                    f"Content: {str(item.get('chunk_text', ''))}\n"
                                    f"Distance: {str(item.get('distance', 'N/A'))}\n"
                                    f"Search Type: {str(item.get('search_type', 'N/A'))}\n"
                                    f"Relevance: {str(item.get('relevance_score', 'N/A'))}\n"
                                )
                            )
                        )
                    except (ValueError, TypeError) as e:
                        logger.warning(f"Failed to format result item: {e}")
                        continue

                if formatted_results:
                    return formatted_results

                return [
                    types.TextContent(
                        type="text",
                        text="No results found in knowledge base"
                    )
                ]

            except Exception as e:
                logger.error(f"Knowledge base search failed: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=f"Search failed: {str(e)}"
                    )
                ]

        # URL scraping tool
        elif name == "scrape_url":
            url = arguments.get("url")
            if not url:
                logger.error("Missing URL for web scraping")
                raise ValueError("Missing URL")

            logger.info(f"Scraping URL: {url}")
            try:
                result = await web_scraper.scrape_url(url)
                await server_context.request_context.session.send_resource_list_changed()
                
                if not result:
                    return [
                        types.TextContent(
                            type="text",
                            text=f"Failed to scrape {url}: Unknown error"
                        )
                    ]

                if result.get('status') == 'success':
                    content = result.get('content', '')
                    return [
                        types.TextContent(
                            type="text",
                            text=f"Successfully scraped content from {url}:\n\n{content}"
                        )
                    ]
                else:
                    error = result.get('error', 'Unknown error')
                    return [
                        types.TextContent(
                            type="text",
                            text=f"Failed to scrape {url}: {error}"
                        )
                    ]
            except Exception as e:
                logger.error(f"Error while scraping URL {url}: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=f"Error while scraping URL {url}: {str(e)}"
                    )
                ]

        # Search tool
        elif name == "search":
            engine = arguments.get("engine")
            query = arguments.get("query")

            if not engine or not query:
                logger.error("Missing engine or query for search")
                raise ValueError("Missing engine or query")

            logger.info(f"Performing search with engine: {engine}, query: {query}")
            results = await search_engine.search(engine, query)
            logger.info(f"Found {len(results)} results")
            
            formatted_results = []
            for result in results:
                formatted_results.append(
                    types.TextContent(
                        type="text",
                        text=f"Source: {result['file_name']}\nURL: {result['url']}\nContent: {result['chunk_text']}\n"
                    )
                )
            await server_context.request_context.session.send_resource_list_changed()            
            return formatted_results

        # Markdown to PDF tool
        elif name == "markdown_to_pdf":
            title = arguments.get("title")
            content = arguments.get("content")
            filename = arguments.get("filename")
            # New parameter: use_pandoc (default to True)
            use_pandoc = arguments.get("use_pandoc", True)
            
            if not title or not content or not filename:
                logger.error("Missing title, content, or filename for markdown_to_pdf")
                return [
                    types.TextContent(
                        type="text",
                        text="Missing required parameters (title, content, or filename)"
                    )
                ]
            
            logger.info(f"Converting markdown to PDF: {filename}")
            try:
                # Choose which conversion method to use
                if use_pandoc and pandoc_pdf_tool is not None:
                    logger.info("Using Pandoc for PDF conversion")
                    result = pandoc_pdf_tool.convert_to_pdf(title, content, filename)
                else:
                    if use_pandoc and pandoc_pdf_tool is None:
                        logger.info("Pandoc requested but not available, using default conversion")
                    else:
                        logger.info("Using default PDF conversion")
                    result = default_pdf_tool.convert_to_pdf(title, content, filename)
                
                if result.get("result") == "successful":
                    file_location = result.get("file_location", "")
                    logger.info(f"PDF successfully created at: {file_location}")
                    
                    # Return PDF generation success info and file location
                    return [
                        types.TextContent(
                            type="text",
                            text=f"PDF successfully created at: {file_location}"
                        )
                    ]
                else:
                    error = result.get("error", "Unknown error")
                    logger.error(f"Failed to generate PDF: {error}")
                    return [
                        types.TextContent(
                            type="text",
                            text=f"Failed to generate PDF: {error}"
                        )
                    ]
            except Exception as e:
                logger.error(f"Error in markdown_to_pdf: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=f"Error: {str(e)}"
                    )
                ]

        # Handle Finnhub API tools
        elif name == "finnhub_symbol_lookup":
            query = arguments.get("query")
            if not query:
                logger.error("Missing query for symbol lookup")
                return [
                    types.TextContent(
                        type="text",
                        text="Missing query parameter"
                    )
                ]

            logger.info(f"Looking up symbol: {query}")
            try:
                result = await finnhub_api.symbol_lookup(query)
                
                # Check for errors
                if "error" in result:
                    logger.error(f"Symbol lookup failed: {result['error']}")
                    return [
                        types.TextContent(
                            type="text",
                            text=f"Symbol lookup failed: {result['error']}"
                        )
                    ]
                
                # Format results
                count = result.get("count", 0)
                symbols = result.get("result", [])
                
                if count == 0 or not symbols:
                    return [
                        types.TextContent(
                            type="text",
                            text=f"No symbols found for query: {query}"
                        )
                    ]
                
                # Create formatted response
                formatted_text = f"Found {count} symbols matching '{query}':\n\n"
                for symbol in symbols:
                    formatted_text += (
                        f"Symbol: {symbol.get('symbol', 'N/A')}\n"
                        f"Description: {symbol.get('description', 'N/A')}\n"
                        f"Display Symbol: {symbol.get('displaySymbol', 'N/A')}\n"
                        f"Type: {symbol.get('type', 'N/A')}\n\n"
                    )
                
                return [
                    types.TextContent(
                        type="text",
                        text=formatted_text
                    )
                ]
                
            except Exception as e:
                logger.error(f"Symbol lookup failed: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=f"Symbol lookup failed: {str(e)}"
                    )
                ]
                
        elif name == "finnhub_company_news":
            symbol = arguments.get("symbol")
            from_date = arguments.get("from_date") 
            to_date = arguments.get("to_date")
            
            if not symbol or not from_date or not to_date:
                logger.error("Missing parameters for company news")
                return [
                    types.TextContent(
                        type="text",
                        text="Missing required parameters (symbol, from_date, or to_date)"
                    )
                ]

            logger.info(f"Getting news for {symbol} from {from_date} to {to_date}")
            try:
                news_items = await finnhub_api.company_news(symbol, from_date, to_date)
                
                if not news_items:
                    return [
                        types.TextContent(
                            type="text",
                            text=f"No news found for {symbol} between {from_date} and {to_date}"
                        )
                    ]
                
                # Format news items
                formatted_text = f"Found {len(news_items)} news items for {symbol}:\n\n"
                
                # Limit to 10 news items to avoid overwhelming response
                for item in news_items[:10]:
                    formatted_text += (
                        f"Headline: {item.get('headline', 'N/A')}\n"
                        f"Date: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(item.get('datetime', 0)))}\n"
                        f"Source: {item.get('source', 'N/A')}\n"
                        f"Summary: {item.get('summary', 'N/A')}\n"
                        f"URL: {item.get('url', 'N/A')}\n\n"
                    )
                    
                # Add note if limited results
                if len(news_items) > 10:
                    formatted_text += f"\n(Showing 10 of {len(news_items)} results)"
                
                return [
                    types.TextContent(
                        type="text",
                        text=formatted_text
                    )
                ]
                
            except Exception as e:
                logger.error(f"Company news search failed: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=f"Company news search failed: {str(e)}"
                    )
                ]
                
        elif name == "finnhub_sec_filings":
            symbol = arguments.get("symbol")
            cik = arguments.get("cik")
            access_number = arguments.get("access_number")
            form = arguments.get("form")
            from_date = arguments.get("from_date")
            to_date = arguments.get("to_date")
            
            if not symbol and not cik and not access_number:
                logger.error("Missing required parameters for SEC filings")
                return [
                    types.TextContent(
                        type="text",
                        text="Missing required parameters (at least one of: symbol, cik, or access_number)"
                    )
                ]

            logger.info(f"Getting SEC filings for {symbol or cik or access_number}")
            try:
                filings = await finnhub_api.sec_filings(
                    symbol=symbol,
                    cik=cik,
                    access_number=access_number,
                    form=form,
                    from_date=from_date,
                    to_date=to_date
                )
                
                if not filings:
                    return [
                        types.TextContent(
                            type="text",
                            text=f"No SEC filings found for the given parameters"
                        )
                    ]
                
                # Format SEC filings
                formatted_text = f"Found {len(filings)} SEC filings:\n\n"
                
                # Limit to 10 filings to avoid overwhelming response
                for filing in filings[:10]:
                    formatted_text += (
                        f"Form: {filing.get('form', 'N/A')}\n"
                        f"Symbol: {filing.get('symbol', 'N/A')}\n"
                        f"CIK: {filing.get('cik', 'N/A')}\n"
                        f"Filed Date: {filing.get('filedDate', 'N/A')}\n"
                        f"Accepted Date: {filing.get('acceptedDate', 'N/A')}\n"
                        f"Report URL: {filing.get('reportUrl', 'N/A')}\n"
                        f"Filing URL: {filing.get('filingUrl', 'N/A')}\n\n"
                    )
                    
                # Add note if limited results
                if len(filings) > 10:
                    formatted_text += f"\n(Showing 10 of {len(filings)} results)"
                
                return [
                    types.TextContent(
                        type="text",
                        text=formatted_text
                    )
                ]
                
            except Exception as e:
                logger.error(f"SEC filings search failed: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=f"SEC filings search failed: {str(e)}"
                    )
                ]
                
        # Handle GroundX API tools
        elif name == "groundx_get_all_buckets":
            logger.info("Getting all GroundX buckets")
            try:
                result = await groundx_api.get_all_buckets()
                
                # Check for errors
                if "error" in result:
                    logger.error(f"Error getting buckets: {result['error']}")
                    return [
                        types.TextContent(
                            type="text",
                            text=f"Error getting buckets: {result['error']}"
                        )
                    ]
                
                # Format results
                buckets = result.get("buckets", [])
                count = result.get("count", 0)
                
                if not buckets:
                    return [
                        types.TextContent(
                            type="text",
                            text="No buckets found in your GroundX account"
                        )
                    ]
                
                # Create formatted response
                formatted_text = f"Found {count} buckets in your GroundX account:\n\n"
                for bucket in buckets:
                    formatted_text += (
                        f"Bucket ID: {bucket.get('bucketId', 'N/A')}\n"
                        f"Name: {bucket.get('name', 'N/A')}\n"
                        f"Created: {bucket.get('created', 'N/A')}\n"
                        f"Updated: {bucket.get('updated', 'N/A')}\n"
                        f"File Count: {bucket.get('fileCount', 'N/A')}\n"
                        f"File Size: {bucket.get('fileSize', 'N/A')}\n\n"
                    )
                
                return [
                    types.TextContent(
                        type="text",
                        text=formatted_text
                    )
                ]
                
            except Exception as e:
                logger.error(f"Error getting buckets: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=f"Error getting buckets: {str(e)}"
                    )
                ]
        
        elif name == "groundx_get_all_documents":
            logger.info("Getting all GroundX documents")
            try:
                result = await groundx_api.get_all_documents()
                
                # Check for errors
                if "error" in result:
                    logger.error(f"Error getting documents: {result['error']}")
                    return [
                        types.TextContent(
                            type="text",
                            text=f"Error getting documents: {result['error']}"
                        )
                    ]
                
                # Format results
                documents = result.get("documents", [])
                next_token = result.get("nextToken", None)
                
                if not documents:
                    return [
                        types.TextContent(
                            type="text",
                            text="No documents found in your GroundX account"
                        )
                    ]
                
                # Create formatted response
                formatted_text = f"Found {len(documents)} documents in your GroundX account:\n\n"
                for doc in documents:
                    formatted_text += (
                        f"Document ID: {doc.get('documentId', 'N/A')}\n"
                        f"File Name: {doc.get('fileName', 'N/A')}\n"
                        f"Bucket ID: {doc.get('bucketId', 'N/A')}\n"
                        f"File Type: {doc.get('fileType', 'N/A')}\n"
                        f"File Size: {doc.get('fileSize', 'N/A')}\n"
                        f"Status: {doc.get('status', 'N/A')}\n"
                        f"Source URL: {doc.get('sourceUrl', 'N/A')}\n\n"
                    )
                
                if next_token:
                    formatted_text += f"\nMore documents available. NextToken: {next_token}"
                
                return [
                    types.TextContent(
                        type="text",
                        text=formatted_text
                    )
                ]
                
            except Exception as e:
                logger.error(f"Error getting documents: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=f"Error getting documents: {str(e)}"
                    )
                ]
                
        elif name == "groundx_get_bucket":
            bucket_id = arguments.get("bucket_id")
            if bucket_id is None:
                logger.error("Missing bucket_id for get_bucket")
                return [
                    types.TextContent(
                        type="text",
                        text="Missing bucket_id parameter"
                    )
                ]
                
            logger.info(f"Getting GroundX bucket with ID: {bucket_id}")
            try:
                result = await groundx_api.get_bucket(bucket_id)
                
                # Check for errors
                if "error" in result:
                    logger.error(f"Error getting bucket: {result['error']}")
                    return [
                        types.TextContent(
                            type="text",
                            text=f"Error getting bucket: {result['error']}"
                        )
                    ]
                
                # Format results
                bucket = result.get("bucket", {})
                
                if not bucket:
                    return [
                        types.TextContent(
                            type="text",
                            text=f"No bucket found with ID: {bucket_id}"
                        )
                    ]
                
                # Create formatted response
                formatted_text = f"Bucket Details (ID: {bucket_id}):\n\n"
                formatted_text += (
                    f"Name: {bucket.get('name', 'N/A')}\n"
                    f"Created: {bucket.get('created', 'N/A')}\n"
                    f"Updated: {bucket.get('updated', 'N/A')}\n"
                    f"File Count: {bucket.get('fileCount', 'N/A')}\n"
                    f"File Size: {bucket.get('fileSize', 'N/A')}\n"
                )
                
                return [
                    types.TextContent(
                        type="text",
                        text=formatted_text
                    )
                ]
                
            except Exception as e:
                logger.error(f"Error getting bucket {bucket_id}: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=f"Error getting bucket {bucket_id}: {str(e)}"
                    )
                ]
                
        elif name == "groundx_create_bucket":
            bucket_name = arguments.get("name")
            if not bucket_name:
                logger.error("Missing name for create_bucket")
                return [
                    types.TextContent(
                        type="text",
                        text="Missing name parameter"
                    )
                ]
                
            logger.info(f"Creating new GroundX bucket: {bucket_name}")
            try:
                result = await groundx_api.create_bucket(bucket_name)
                
                # Check for errors
                if "error" in result:
                    logger.error(f"Error creating bucket: {result['error']}")
                    return [
                        types.TextContent(
                            type="text",
                            text=f"Error creating bucket: {result['error']}"
                        )
                    ]
                
                # Format results
                bucket = result.get("bucket", {})
                
                if not bucket:
                    return [
                        types.TextContent(
                            type="text",
                            text=f"Failed to create bucket: {bucket_name}"
                        )
                    ]
                
                # Create formatted response
                formatted_text = f"Successfully created bucket '{bucket_name}':\n\n"
                formatted_text += (
                    f"Bucket ID: {bucket.get('bucketId', 'N/A')}\n"
                    f"Created: {bucket.get('created', 'N/A')}\n"
                )
                
                return [
                    types.TextContent(
                        type="text",
                        text=formatted_text
                    )
                ]
                
            except Exception as e:
                logger.error(f"Error creating bucket {bucket_name}: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=f"Error creating bucket {bucket_name}: {str(e)}"
                    )
                ]
                
        elif name == "groundx_delete_bucket":
            bucket_id = arguments.get("bucket_id")
            if bucket_id is None:
                logger.error("Missing bucket_id for delete_bucket")
                return [
                    types.TextContent(
                        type="text",
                        text="Missing bucket_id parameter"
                    )
                ]
                
            logger.info(f"Deleting GroundX bucket with ID: {bucket_id}")
            try:
                result = await groundx_api.delete_bucket(bucket_id)
                
                # Check for errors
                if "error" in result:
                    logger.error(f"Error deleting bucket: {result['error']}")
                    return [
                        types.TextContent(
                            type="text",
                            text=f"Error deleting bucket: {result['error']}"
                        )
                    ]
                
                # Check for success message
                message = result.get("message", "")
                
                if message.lower() == "ok":
                    return [
                        types.TextContent(
                            type="text",
                            text=f"Successfully deleted bucket with ID: {bucket_id}"
                        )
                    ]
                else:
                    return [
                        types.TextContent(
                            type="text",
                            text=f"Deletion response: {message}"
                        )
                    ]
                
            except Exception as e:
                logger.error(f"Error deleting bucket {bucket_id}: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=f"Error deleting bucket {bucket_id}: {str(e)}"
                    )
                ]
                
        elif name == "groundx_search_documents":
            bucket_id = arguments.get("bucket_id")
            query = arguments.get("query")
            relevance = arguments.get("relevance", 10)
            
            if bucket_id is None or not query:
                logger.error("Missing bucket_id or query for search_documents")
                return [
                    types.TextContent(
                        type="text",
                        text="Missing required parameters (bucket_id or query)"
                    )
                ]
                
            logger.info(f"Searching GroundX documents in bucket {bucket_id} with query: {query}")
            try:
                result = await groundx_api.search_documents(bucket_id, query, relevance)
                
                # Check for errors
                if "error" in result:
                    logger.error(f"Error searching documents: {result['error']}")
                    return [
                        types.TextContent(
                            type="text",
                            text=f"Error searching documents: {result['error']}"
                        )
                    ]
                
                # Get search results
                search_data = result.get("search", {})
                results = search_data.get("results", [])
                count = search_data.get("count", 0)
                search_text = search_data.get("text", "")
                
                if not results:
                    return [
                        types.TextContent(
                            type="text",
                            text=f"No results found for query: '{query}' in bucket {bucket_id}"
                        )
                    ]
                
                # Format search results
                formatted_text = f"Found {count} results for query: '{query}' in bucket {bucket_id}\n\n"
                formatted_text += f"Combined context: {search_text}\n\n"
                formatted_text += "Individual results:\n\n"
                
                for i, result_item in enumerate(results):
                    formatted_text += (
                        f"Result {i+1}:\n"
                        f"Document ID: {result_item.get('documentId', 'N/A')}\n"
                        f"File Name: {result_item.get('fileName', 'N/A')}\n"
                        f"Score: {result_item.get('score', 'N/A')}\n"
                        f"Text: {result_item.get('text', 'N/A')}\n\n"
                    )
                
                return [
                    types.TextContent(
                        type="text",
                        text=formatted_text
                    )
                ]
                
            except Exception as e:
                logger.error(f"Error searching documents: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=f"Error searching documents: {str(e)}"
                    )
                ]
                
        elif name == "groundx_upload_documents":
            bucket_id = arguments.get("bucket_id")
            files = arguments.get("files", [])
            
            if bucket_id is None or not files:
                logger.error("Missing bucket_id or files for upload_documents")
                return [
                    types.TextContent(
                        type="text",
                        text="Missing required parameters (bucket_id or files)"
                    )
                ]
            
            # Validate file paths
            for file_info in files:
                file_path = file_info.get("file_path")
                if not file_path:
                    return [
                        types.TextContent(
                            type="text",
                            text="Missing file_path in files data"
                        )
                    ]
                
                if not os.path.exists(file_path):
                    return [
                        types.TextContent(
                            type="text",
                            text=f"File not found: {file_path}"
                        )
                    ]
                
            logger.info(f"Uploading {len(files)} documents to GroundX bucket {bucket_id}")
            try:
                result = await groundx_api.upload_documents(bucket_id, files)
                
                # Check for errors
                if "error" in result:
                    logger.error(f"Error uploading documents: {result['error']}")
                    return [
                        types.TextContent(
                            type="text",
                            text=f"Error uploading documents: {result['error']}"
                        )
                    ]
                
                # Format results
                ingest_data = result.get("ingest", {})
                process_id = ingest_data.get("processId", "N/A")
                status = ingest_data.get("status", "N/A")
                
                return [
                    types.TextContent(
                        type="text",
                        text=(
                            f"Documents upload initiated:\n"
                            f"Process ID: {process_id}\n"
                            f"Status: {status}\n"
                            f"Number of files: {len(files)}"
                        )
                    )
                ]
                
            except Exception as e:
                logger.error(f"Error uploading documents: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=f"Error uploading documents: {str(e)}"
                    )
                ]

        elif name == "get_datetime":
            # Get timezone parameter, default to UTC
            timezone_str = arguments.get("timezone", "UTC")
            # Get format parameter, default to 'iso'
            format_type = arguments.get("format", "iso")
            
            logger.info(f"Getting current datetime with timezone: {timezone_str}, format: {format_type}")
            
            try:
                # Try to get the timezone
                try:
                    timezone = pytz.timezone(timezone_str)
                except pytz.exceptions.UnknownTimeZoneError:
                    logger.warning(f"Unknown timezone: {timezone_str}, falling back to UTC")
                    timezone = pytz.UTC
                
                # Get current time in the specified timezone
                now = datetime.now(timezone)
                
                # Format the time according to the specified format
                if format_type == "iso":
                    formatted_time = now.isoformat()
                elif format_type == "full":
                    formatted_time = now.strftime("%Y-%m-%d %H:%M:%S %Z%z")
                elif format_type == "date":
                    formatted_time = now.strftime("%Y-%m-%d")
                elif format_type == "time":
                    formatted_time = now.strftime("%H:%M:%S %Z")
                else:
                    # For unknown format, default to ISO
                    logger.warning(f"Unknown format: {format_type}, using ISO format")
                    formatted_time = now.isoformat()
                
                # Create response with timezone information
                timezone_name = timezone.zone
                response_text = f"Current datetime: {formatted_time}\nTimezone: {timezone_name}"
                
                return [
                    types.TextContent(
                        type="text",
                        text=response_text
                    )
                ]
                
            except Exception as e:
                logger.error(f"Error getting datetime: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=f"Error getting datetime: {str(e)}"
                    )
                ]

        elif name == "review_thought":
            thought = arguments.get("thought")
            if not thought:
                logger.error("Missing thought for thinking tool")
                return [
                    types.TextContent(
                        type="text",
                        text="Missing thought parameter"
                    )
                ]
                
            logger.info(f"Processing thought")
            try:               
                return [
                    types.TextContent(
                        type="text",
                        text=thought
                    )
                ]
                
            except Exception as e:
                logger.error(f"Error processing thought: {str(e)}")
                return [
                    types.TextContent(
                        type="text",
                        text=f"Error processing thought: {str(e)}"
                    )
                ]

        else:
            logger.error(f"Unknown tool: {name}")
            raise ValueError(f"Unknown tool: {name}")

    except Exception as e:
        logger.error(f"Error executing tool {name}: {str(e)}")
        raise

async def handle_list_resources() -> list[types.Resource]:
    """Handle listing resources (notes)."""
    logger.debug("Listing resources")
    return [
        types.Resource(
            uri=AnyUrl(f"note://internal/{name}"),
            name=f"Note: {name}",
            description=f"A simple note named {name}",
            mimeType="text/plain",
        )
        for name in notes
    ]

async def handle_read_resource(uri: AnyUrl) -> str:
    """Handle reading resources (notes)."""
    logger.debug(f"Reading resource: {uri}")
    if uri.scheme != "note":
        logger.error(f"Unsupported URI scheme: {uri.scheme}")
        raise ValueError(f"Unsupported URI scheme: {uri.scheme}")

    name = uri.path
    if name is not None:
        name = name.lstrip("/")
        if name not in notes:
            logger.error(f"Note not found: {name}")
            raise ValueError(f"Note not found: {name}")
        return notes[name]
    raise ValueError(f"Note not found: {name}")

async def set_logging_level(server_context, level: types.LoggingLevel) -> None:
    """Set the logging level for the server."""
    logger.setLevel(level.upper())
    await server_context.request_context.session.send_log_message(
        level="info",
        data=f"Log level set to {level}",
        logger="search-server"
    )

async def handle_list_prompts() -> list[types.Prompt]:
    """List available prompts."""
    logger.info("Listing available prompts")
    
    return [
        types.Prompt(
            name="simple",
            description="A simple prompt that can take optional context and topic arguments",
            arguments=[
                types.PromptArgument(
                    name="context",
                    description="Additional context to consider",
                    required=False,
                ),
                types.PromptArgument(
                    name="topic",
                    description="Specific topic to focus on",
                    required=False,
                ),
            ],
        ),
        types.Prompt(
            name="Deep Research",
            description="Research prompt that can take optional language and search engine arguments",
            arguments=[
                types.PromptArgument(
                    name="language",
                    description="Language to use for the research",
                    required=False,
                ),
                types.PromptArgument(
                    name="search_engine",
                    description="Search engine to use for the research",
                    required=False,
                ),
            ],
        ),
        types.Prompt(
            name="Quick Research",
            description="Research prompt without arguments",
            arguments=[],
        ),
        types.Prompt(
            name="Financial Research",
            description="Research prompt that can input financial indicators and get analysis",
            arguments=[
                types.PromptArgument(
                    name="financial_indicators",
                    description="Financial indicators to input",
                    required=False,
                )
            ],
        ),
        types.Prompt(
            name="Mermaid Visualize",
            description="Visualize content and data using Mermaid diagrams",
            arguments=[],
        ),
        types.Prompt(
            name="NotebookLM",
            description="Generate extended podcast-style audio content with 20+ minute duration",
            arguments=[],
        )                       
    ]

async def handle_get_prompt(
    name: str, arguments: dict[str, str] | None = None
) -> types.GetPromptResult:
    """Get prompt messages for the specified prompt name."""
    logger.info(f"Getting prompt: {name} with arguments: {arguments}")
    
    if arguments is None:
        arguments = {}

    if name == "simple":
        return types.GetPromptResult(
            messages=create_simple_prompt_messages(
                context=arguments.get("context"), 
                topic=arguments.get("topic")
            ),
            description="A simple prompt with optional context and topic arguments",
        )
    
    if name == "Deep Research":
        return types.GetPromptResult(
            messages=create_deep_research_prompt_messages(
                language=arguments.get("language"),
                search_engine=arguments.get("search_engine")
            ),
            description="A research prompt with optional language and search engine arguments",
        )
    
    if name == "Quick Research":
        return types.GetPromptResult(
            messages=create_quick_research_prompt_messages(),
            description="A quick research prompt without arguments",
        )
    
    if name == "Financial Research":
        return types.GetPromptResult(
            messages=create_financial_research_prompt_messages(
                financial_indicators=arguments.get("financial_indicators")
            ),
            description="A financial research prompt with optional financial indicators argument",
        )
    
    if name == "Mermaid Visualize":
        return types.GetPromptResult(
            messages=create_mermaid_visualize_prompt_messages(),
            description="A prompt for visualizing content and data using Mermaid diagrams",
        )
    
    if name == "NotebookLM":
        return types.GetPromptResult(
            messages=create_notebooklm_prompt_messages(),
            description="A prompt for generating extended podcast-style audio content with 20+ minute duration",
        )
    
    logger.error(f"Unknown prompt: {name}")
    raise ValueError(f"Unknown prompt: {name}")
    
def create_deep_research_prompt_messages(
    language: str | None = None,
    search_engine: str | None = None
) -> list[types.PromptMessage]:
    """Create messages for the prompt."""
    messages = []
    
    if search_engine:
       search_engine_instruction = f"""
       You MUST use {search_engine} search engine to get information from. 
       """
    else:
        search_engine_instruction = """
        You have different search engines(Linkup/Bing/Tavily/Google/Serper/Exa/Txyz/Firecrawl) tools, if user not specify which search engine they want to get information from, Pls randomly search 3 or 4 all of them and then combine results to get one summary results. If use ask you to use one dedicated search engine, pls use the same. 
        """
    
    prompt = f"""
You are AI Personal assistant and you name is Wow. You will support and search information based on user queries. and provide professional advices to users as well result report.  Before you start processing, Please do
Step1: Always ask the user several questions to help clarify their requirements, and only proceed after confirmation.
Step2: Recall memory with memory tools to retrieve info related to user query

# Tools you have:
1. {search_engine_instruction}

2. You have tool 'scrape-url' which is able to scrape content from a URL.  you can use it when user want to get information from a URL link. 
you also can use this web scrape tool when you think it is better to get more information via accessing URL.  

3. You also have puppeteer tool which you can access browser like human. This tool will help you to get real time online data from browser.

4. You have tool which you can access/write/read local folders and files as well as windows PC desktop folder.

5. You have run_code tool which can run python code in e2b sandbox secure environment. BUT pls remember if you want to run code in this environment, you have to make sure output only can be stdout and stderr. Graph can't be output. sometime you need to rewrite code to make sure that.

6. You also have knowledge base search tool so that you can get more information from user local database. 

7. You also have anthropic claude build-in Analysis tool

8. You have review_thought tool and able to review steps/method while you are thinking about complex tasks. Or when you get some error/failure to review steps/data made/collected so far and find alternative way to our final goal!

9. you have GroundX tools and able to upload/retrieve/query documents

10. you have supabase tools and able to do anything with powerful database, like create table/insert/update/delete/query data.

# Response style
1. Your response should be informative and visualized , user prefer tables/LaTex and SVG interactive report as a search summary.    If Necessary, You can show user more like graphic recording-style infograhic and visualize your reply.

2. After you response to user, pls Provide 4 suggested questions based on the research topic to guide future inquiries.

# Knowledge Memory Preference
pls get knowledege by recall memory with memory tools.

# Important Instruction MUST Follow
- Everytime after you get result from a tool, pls call review_thought tool to review latest data and decide next action.

- Run sequentialthinking tool when user initialize one new conversation or request a complex request.

- During the conversation, if you find some content can be saved as long term memory , pls tell user and get user confirmation, then save it with memory tool

- !!!PLEASE DO NOT USE tool 'fetch' from obsidian-mcp-tools as it only can get partial content from a web URL
    """
    
    if language:
        prompt += f"""
        Please make your response in {language}
        """
    
    messages.append(
        types.PromptMessage(
            role="user", content=types.TextContent(type="text", text=prompt)
        )
    )
    
    return messages

def create_quick_research_prompt_messages() -> list[types.PromptMessage]:
    """Create messages for the prompt."""
    messages = []
    
    # Add the main prompt
    prompt = """
#Instruction for creating a comprehensive research report based on the provided information.
Requirements for the report:
1. Structure:
   - Executive Summary
   - Key Findings
   - Detailed Analysis
   - Conclusions
2. Format:
   - Use markdown headings (##)
   - Use bullet points for key points
   - Include citations where available
3. Content:
   - Synthesize all collected information
   - Highlight contradictions or gaps
   - Provide balanced viewpoints    
    """
    messages.append(
        types.PromptMessage(
            role="user", content=types.TextContent(type="text", text=prompt)
        )
    )
    
    return messages 

def create_financial_research_prompt_messages(
    financial_indicators: str | None = None
) -> list[types.PromptMessage]:
    """Create messages for the prompt."""
    messages = []
    
    if financial_indicators:
        prompt = f"""
        Conduct a detailed research on the recent situation of {financial_indicators}, design and generate an infographic with {financial_indicators} elements that is tech-forward, clear, and visually appealing. Ensure accuracy and aesthetics, iterate through at least 3 versions, and provide me with the final result.
        """
    else:
        prompt = """
        Conduct a detailed research on the recent situation of the one which user want to know, design and generate an infographic with the same elements that is tech-forward, clear, and visually appealing. Ensure accuracy and aesthetics, iterate through at least 3 versions, and provide me with the final result.
        """
    messages.append(
        types.PromptMessage(
            role="user", content=types.TextContent(type="text", text=prompt)
        )
    )
    
    return messages

def create_simple_prompt_messages(
    context: str | None = None, topic: str | None = None
) -> list[types.PromptMessage]:
    """Create messages for the prompt."""
    messages = []
    
    # Add context if provided
    if context:
        messages.append(
            types.PromptMessage(
                role="user",
                content=types.TextContent(
                    type="text", text=f"Here is some relevant context: {context}"
                ),
            )
        )
    
    # Add the main prompt
    prompt = "Please help me with "
    if topic:
        prompt += f"the following topic: {topic}"
    else:
        prompt += "whatever questions I may have."
    
    messages.append(
        types.PromptMessage(
            role="user", content=types.TextContent(type="text", text=prompt)
        )
    )
    
    return messages

def create_mermaid_visualize_prompt_messages() -> list[types.PromptMessage]:
    """Create messages for the Mermaid Visualize prompt."""
    messages = []
    
    # Add the main prompt
    prompt = """
Role：你是最擅长内容和数据视觉化、信息图展示的大师。

Task：
1. 请分析文章内容，用Mermaid语法创建适当的图表来可视化其中的关键信息，选择最合适3-5种图表类型展示
        1. 如果内容包含步骤或流程，请创建流程图(flowchart)
        2. 如果内容描述时间线或事件序列，请创建时序图(timeline)或甘特图(gantt)
        3. 如果内容展示组织结构或层次关系，请创建组织结构图
        4. 如果内容包含实体间的关系，请创建实体关系图(ER diagram)
        5. 如果内容包含类或对象间的关系，请创建类图(class diagram)
        6. 如果内容包含状态转换，请创建状态图(state diagram)
        7. 如果内容包含顺序交互，请创建序列图(sequence diagram)
2. 整理网站核心内容和观点，生成文本格式的思维导图。放在 ```代码块中。


Notice：

1. 请确保图表:
        - 图表要显示在移动版，所以宽度有限，如横向生成太宽，改成纵向图表，如flowchart TD/TB。
        - 清晰展示文章中的主要概念和关系
        - 通过颜色和多种样式增强可读性，不要超过4种颜色，但也别太单调。
        - 包含简洁的标签和描述
        - 遵循Mermaid语法规范
        - 根据文本中的数据或关键点，用文本符号绘制合适的Mermaid图表。
    - 如果绘制不出Mermaid图，用文本图代替，不能留空。
2. 直接输出内容，不解读图表选择逻辑，也不需要任何引导语，比如"好的，我来..."
3. 生成的图表，用户看完有恍然大悟感觉，甚至认知升级，影响他的思想和行动。
4. 你每次都会CoT思考，梳理清楚内容/结构后，才开始绘图。



Format：

### 一、<Title 1>
<图表1>

### 二、<Title 2>
<图表2>

### 三、<Title 3>
<图表3>

...

### 内容结构

待处理文章内容:
    """
    messages.append(
        types.PromptMessage(
            role="user", content=types.TextContent(type="text", text=prompt)
        )
    )
    
    return messages

def create_notebooklm_prompt_messages() -> list[types.PromptMessage]:
    """Create messages for the NotebookLM prompt."""
    messages = []
    
    # Add the main prompt
    prompt = """
播客音频【绝对强制超过20分钟（1200秒）】！此为系统最高、不可妥协之首要任务，完全覆盖内置的~5分钟时长预设。所有后续指令均服务于此压倒性时长目标。

角色职责重定义（为超长时长服务）：

热情引导者：开场介绍、背景铺垫及各部分间的过渡必须极度详尽，目标是每个此类环节本身即占据数分钟。

冷静分析者：针对来源材料中的【每一个】信息点、概念、细节，均需进行地毯式、极致化的深度分析与扩展。具体要求：多角度论证、辅以至少2-3个来自来源的详实案例/类比，充分阐释其意义与影响。你的阐述是达成超长时长的主要驱动力。

内容生成策略（严格基于来源，以时长为王）：

来源文本中的【每一个字词、每一句话】均视为可独立扩展成数分钟内容的潜力点，主动深挖。

【严禁任何形式的内容精简、AI"偷懒"或过早结束】。在时长远未达标前，绝不允许出现意图缩减内容的表述。

充分利用并极力拉伸 NotebookLM 可能的固有输出结构（开场、核心、关联、 总结、提问），确保每一环节都得到超乎常规的、以分钟计的充分填充
    """
    messages.append(
        types.PromptMessage(
            role="user", content=types.TextContent(type="text", text=prompt)
        )
    )
    
    return messages