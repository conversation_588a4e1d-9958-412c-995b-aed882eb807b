"""
Search server package providing various server implementations for MCP.

This package contains:
- Direct server: Simple server without OAuth (direct_server_refactored)
- Direct OAuth server: Server with simplified OAuth flow (direct_oauth_server_refactored)
- Google OAuth server: Server with Google OAuth integration (google_oauth_server_refactored)
- Legacy implementations for backward compatibility
"""
from . import server
import asyncio

# Import refactored implementations
from .direct_server import create_direct_server, run_direct_server, run_stdio_direct_server
from .direct_oauth_server import create_direct_oauth_server, run_direct_oauth_server
from .google_oauth_server import create_google_oauth_server, run_google_oauth_server

def main():
    """Main entry point for the package."""
    asyncio.run(server.main())

# Expose important items at package level
__all__ = [
    'main', 'server',
    'create_direct_server', 'run_direct_server', 'run_stdio_direct_server',
    'create_direct_oauth_server', 'run_direct_oauth_server',
    'create_google_oauth_server', 'run_google_oauth_server'
]