"""
Google OAuth MCP server implementation with StreamableHTTP support.

This implementation combines the direct server pattern with Google OAuth authentication
and adds StreamableHTTP support. It addresses several key issues:

1. Uses the direct server pattern from direct_server.py that correctly exposes tools to Claude
2. Implements complete OAuth compatibility with token generation and validation
3. <PERSON><PERSON> requests properly using StreamableHTTP for improved streaming responses
4. Provides special handling for <PERSON> requests to ensure compatibility

This implementation uses the refactored base server component with StreamableHTTP transport.
"""
import os
import json
import time
import secrets
import requests
import urllib.parse
import contextlib
import logging
from typing import Any, Dict, List, Optional
from datetime import datetime
from collections.abc import AsyncIterator

import anyio
import uvicorn
from mcp.server.lowlevel import Server
from mcp.server.streamable_http_manager import StreamableHTTPSessionManager
import mcp.types as types
from pydantic import AnyUrl, AnyHttpUrl
from starlette.applications import Starlette
from starlette.routing import Mount
from starlette.types import Receive, Scope, Send
from termcolor import colored

# Add google-auth imports for ID token validation
from google.oauth2 import id_token as google_id_token_verifier
from google.auth.transport import requests as google_auth_requests

# Import necessary OAuth components - these must be at the top level for correct imports
from mcp.server.auth.provider import (
    AccessToken,
    AuthorizationCode,
    AuthorizationParams,
    OAuthAuthorizationServerProvider, 
    RefreshToken,
    construct_redirect_uri,
)
from mcp.shared.auth import OAuthClientInformationFull, OAuthToken

from .logger import logger
from .token_bridge import shared_authenticated_tokens
from .search_engine import SearchEngine
from .web_scraper import WebScraper
from .knowledge_base import KnowledgeBase
from .markdown_to_pdf import MarkdownToPdfTool
from .finnhub_api import FinnhubAPI
from .groundx_api import GroundXAPI
from .utils.tools import get_tool_definitions
from .utils.handlers import handle_call_tool, handle_list_resources, handle_read_resource, set_logging_level
from .utils.event_store import InMemoryEventStore

# Constants
CLAUDE_CLIENT_ID = os.getenv("CLAUDE_CLIENT_ID", "********-ff92-4c28-b15f-32186412aacd")
GOOGLE_AUTHORIZATION_URL = "https://accounts.google.com/o/oauth2/v2/auth"
GOOGLE_TOKEN_URL = "https://oauth2.googleapis.com/token"
GOOGLE_USERINFO_URL = "https://www.googleapis.com/oauth2/v3/userinfo"
PANDOC_AVAILABLE = False
try:
    from .pandoc_markdown_to_pdf import PandocMarkdownToPdfTool
    PANDOC_AVAILABLE = True
except ImportError:
    PANDOC_AVAILABLE = False

class GoogleOAuthProvider(OAuthAuthorizationServerProvider):
    """Google OAuth provider implementation for the server"""

    def __init__(self):
        self.clients: dict[str, OAuthClientInformationFull] = {}
        self.auth_codes: dict[str, AuthorizationCode] = {}
        self.tokens: dict[str, AccessToken] = {}
        self.refresh_tokens: dict[str, RefreshToken] = {}
        
        # Dictionary to store Google-specific auth information
        self.google_auth_info: dict[str, dict] = {}
        
        # Share tokens with the token bridge
        self.token_bridge = shared_authenticated_tokens
        
        logger.info(colored("GoogleOAuthProvider initialized successfully", "green"))

    async def get_client(self, client_id: str) -> Optional[OAuthClientInformationFull]:
        """Get OAuth client information."""
        logger.debug(f"Looking up client: {client_id}")
        return self.clients.get(client_id)

    async def register_client(self, client_info: OAuthClientInformationFull):
        """Register a new OAuth client."""
        logger.info(colored(f"Registering client: {client_info.client_id}", "green"))
        self.clients[client_info.client_id] = client_info

    async def authorize(self, client: OAuthClientInformationFull, params: AuthorizationParams) -> str:
        """Constructs the redirect URL to Google's authorization server."""
        google_client_id = os.getenv("GOOGLE_OAUTH_CLIENT_ID")
        google_redirect_uri = os.getenv("GOOGLE_OAUTH_REDIRECT_URI")

        if not google_client_id or not google_redirect_uri:
            logger.error(colored("Google OAuth environment variables (GOOGLE_OAUTH_CLIENT_ID, GOOGLE_OAUTH_REDIRECT_URI) not set", "red"))
            raise ValueError("Google OAuth environment variables not set")

        state = params.state or secrets.token_hex(16)
        # Store state to verify later in the callback. Using auth_codes dict for simplicity for now.
        # A more robust solution might use a dedicated state store with TTL.
        
        # Get code_challenge_method with a default of 'S256' if not present
        code_challenge_method = getattr(params, 'code_challenge_method', 'S256') or 'S256'
        
        self.auth_codes[state] = AuthorizationCode(
            code=state, # Using state as a temporary holder for itself
            client_id=client.client_id, # This would be our app's client_id if we distinguish
            redirect_uri=params.redirect_uri, # The original redirect_uri requested by client
            redirect_uri_provided_explicitly=True, # Add this required field
            expires_at=time.time() + 300,  # 5 minutes for state validity
            scopes=params.scopes or [],
            code_challenge=params.code_challenge,
            code_challenge_method=code_challenge_method
        )

        query_params = {
            "client_id": google_client_id,
            "redirect_uri": google_redirect_uri, # This is OUR redirect_uri registered with Google
            "response_type": "code",
            "scope": "openid email profile", # Standard Google scopes
            "state": state,
            "access_type": "offline",  # To get a refresh token
            "prompt": "consent",       # Ensures the consent screen is shown, good for testing
        }
        
        redirect_url = f"{GOOGLE_AUTHORIZATION_URL}?{urllib.parse.urlencode(query_params)}"
        
        logger.info(colored(f"Redirecting to Google for authorization: {redirect_url}", "blue"))
        return redirect_url

    async def load_authorization_code(
        self, client: OAuthClientInformationFull, authorization_code_or_state: str
    ) -> Optional[AuthorizationCode]:
        """Load an authorization code (our MCP code) or a stored state parameter."""
        logger.debug(f"Loading authorization_code_or_state: {authorization_code_or_state[:10]}...")
        return self.auth_codes.get(authorization_code_or_state)

    async def exchange_authorization_code(
        self, client: OAuthClientInformationFull, mcp_authorization_code: AuthorizationCode, code_verifier: Optional[str] = None
    ) -> OAuthToken:
        """Exchange our MCP authorization code for an MCP token. PKCE verification for non-Claude clients."""
        logger.info(colored(f"Exchanging MCP authorization code: {mcp_authorization_code.code[:10]}... for client {client.client_id}", "green"))

        # PKCE verification if a challenge was associated with the MCP auth code
        # This happens if the original /authorize request from Claude included PKCE params.
        # Safely get code challenge with fallbacks
        code_challenge = getattr(mcp_authorization_code, 'code_challenge', None)
        code_challenge_method = getattr(mcp_authorization_code, 'code_challenge_method', 'S256')
        if code_challenge and code_challenge_method:
            if client.client_id == CLAUDE_CLIENT_ID:
                logger.info(colored("Bypassing PKCE verification for Claude client on MCP code exchange", "yellow"))
            elif not code_verifier:
                logger.error(colored("PKCE verification required for MCP code but no code_verifier provided", "red"))
                raise ValueError("PKCE verification required for MCP code but no code_verifier provided")
            else:
                import base64
                import hashlib
                verifier_bytes = code_verifier.encode("utf-8") # Google examples use utf-8
                digest = hashlib.sha256(verifier_bytes).digest()
                computed_challenge = base64.urlsafe_b64encode(digest).decode("utf-8").rstrip("=")
                
                if computed_challenge != code_challenge:
                    logger.error(colored("PKCE verification failed for MCP code", "red"))
                    raise ValueError("PKCE verification failed for MCP code")
                logger.info(colored("PKCE verification successful for MCP code", "green"))
        
        # Generate our MCP token
        mcp_token_str = f"mcp_{secrets.token_hex(32)}"
        
        # Store MCP token
        internal_token = AccessToken(
            token=mcp_token_str,
            client_id=client.client_id,
            scopes=mcp_authorization_code.scopes,
            expires_at=int(time.time()) + 2592000,  # 30 days for MCP token
        )
        self.tokens[mcp_token_str] = internal_token
        
        # Store in token bridge if needed
        token_info = {
            "client_id": client.client_id,
            "scopes": mcp_authorization_code.scopes,
            "created_at": datetime.now(),
        }
        
        # Add Google user info if available
        google_info = self.google_auth_info.get(mcp_authorization_code.code, {})
        if 'google_user_email' in google_info:
            token_info["google_user"] = google_info['google_user_email']
            
        self.token_bridge[mcp_token_str] = token_info
        
        # Create MCP refresh token
        mcp_refresh_token_str = f"mcp_refresh_{secrets.token_hex(16)}"
        
        self.refresh_tokens[mcp_refresh_token_str] = RefreshToken(
            token=mcp_refresh_token_str,
            client_id=client.client_id,
            scopes=mcp_authorization_code.scopes,
        )
        
        # Remove used MCP authorization code
        if mcp_authorization_code.code in self.auth_codes:
            del self.auth_codes[mcp_authorization_code.code]
            
        # Clean up Google auth info if it exists
        google_auth_info = getattr(self, 'google_auth_info', {})
        if mcp_authorization_code.code in google_auth_info:
            del google_auth_info[mcp_authorization_code.code]
        
        oauth_token_response = OAuthToken(
            access_token=mcp_token_str,
            token_type="bearer",
            expires_in=2592000, # 30 days
            scope=" ".join(mcp_authorization_code.scopes),
            refresh_token=mcp_refresh_token_str,
        )
        
        logger.info(colored(f"MCP Token issued: {mcp_token_str[:10]}...", "green"))
        return oauth_token_response

    async def load_access_token(self, token: str) -> Optional[AccessToken]:
        """Load and validate an access token."""
        access_token = self.tokens.get(token)
        if not access_token:
            logger.debug(f"Token not found: {token[:10]}...")
            return None
        
        # Check if expired
        if access_token.expires_at and access_token.expires_at < time.time():
            logger.debug(f"Token expired: {token[:10]}...")
            del self.tokens[token]
            return None
        
        logger.debug(f"Token validated: {token[:10]}...")
        return access_token

    async def load_refresh_token(
        self, client: OAuthClientInformationFull, refresh_token: str
    ) -> Optional[RefreshToken]:
        """Load a refresh token"""
        return self.refresh_tokens.get(refresh_token)

    async def exchange_refresh_token(
        self,
        client: OAuthClientInformationFull,
        refresh_token: RefreshToken,
        scopes: Optional[list[str]] = None,
    ) -> OAuthToken:
        """Exchange refresh token for new access token"""
        # Generate new token
        mcp_token = f"mcp_{secrets.token_hex(32)}"
        
        # Use original scopes if none provided
        token_scopes = scopes or refresh_token.scopes
        
        # Store token
        token = AccessToken(
            token=mcp_token,
            client_id=client.client_id,
            scopes=token_scopes,
            expires_at=int(time.time()) + 2592000,  # 30 days
        )
        self.tokens[mcp_token] = token
        
        # Store in token bridge
        self.token_bridge[mcp_token] = {
            "client_id": client.client_id,
            "scopes": token_scopes,
            "created_at": datetime.now(),
        }
        
        # Create and return OAuth token
        oauth_token = OAuthToken(
            access_token=mcp_token,
            token_type="bearer",
            expires_in=2592000,  # 30 days
            scope=" ".join(token_scopes),
            refresh_token=refresh_token.token,  # Keep same refresh token
        )
        
        logger.info(colored(f"Refreshed token issued: {mcp_token[:10]}...", "green"))
        return oauth_token

    async def validate_google_id_token(self, token_str: str, audience_client_id: str) -> Optional[Dict[str, Any]]:
        """Validates Google ID token and returns claims if valid."""
        try:
            # Verify the ID token while checking the audience
            certs_request = google_auth_requests.Request()

            # Verify the token against Google's public keys
            id_info = google_id_token_verifier.verify_oauth2_token(
                token_str, certs_request, audience_client_id, clock_skew_in_seconds=10
            )
            
            if id_info.get('iss') not in ['accounts.google.com', 'https://accounts.google.com']:
                raise ValueError('Wrong issuer.')
                
            user_email = id_info.get('email')
            logger.info(colored(f"Google ID token validated successfully for email: {user_email}", "green"))
            return id_info
        except ValueError as e:
            logger.error(colored(f"Google ID token validation error: {str(e)}", "red"))
            return None
        except Exception as e:
            logger.error(colored(f"Unexpected error during Google ID token validation: {str(e)}", "red"))
            return None

class GoogleOAuthStreamServer:
    """Google OAuth server implementation with StreamableHTTP transport."""
    
    def __init__(self, server_name="google_oauth_stream_server"):
        """Initialize the Google OAuth server with OAuth provider and StreamableHTTP."""
        self.server_name = server_name
        logger.info(colored(f"Creating {server_name} with StreamableHTTP transport", "green"))
        
        # Create OAuth provider
        self.oauth_provider = GoogleOAuthProvider()
        
        # Set up StreamableHTTP MCP server
        self.app = Server(server_name)
        
        # Initialize server components
        self._initialize_components()
        
        # Register handlers with the server
        self._register_handlers()
        
        # Create event store for resumability
        self.event_store = InMemoryEventStore()
        
        # Determine if we should use JSON responses instead of SSE streams
        json_response = os.getenv("STREAMABLE_HTTP_JSON_RESPONSE", "false").lower() == "true"
        
        # Create session manager with StreamableHTTP
        self.session_manager = StreamableHTTPSessionManager(
            app=self.app,
            event_store=self.event_store,  # Enable resumability
            json_response=json_response,
        )
        
        # Authentication middleware for StreamableHTTP
        self.original_handle_request = self.session_manager.handle_request
        
        # Override the handle_request method to add authentication
        async def authenticated_handle_request(scope: Scope, receive: Receive, send: Send) -> None:
            if not self.authenticate_request(scope):
                # Send 401 Unauthorized response
                await send({
                    "type": "http.response.start",
                    "status": 401,
                    "headers": [
                        (b"content-type", b"text/plain"),
                        (b"www-authenticate", b'Bearer realm="search_server"'),
                    ],
                })
                await send({
                    "type": "http.response.body",
                    "body": b"Unauthorized: Invalid or missing authentication token",
                })
                return
            
            # If authenticated, proceed with original handler
            await self.original_handle_request(scope, receive, send)
        
        # Replace the handle_request method
        self.session_manager.handle_request = authenticated_handle_request
        
        # Store custom endpoints for OAuth handling
        self.oauth_endpoints = {
            "/token": self.handle_token_endpoint,
            "/oauth/callback": self.handle_oauth_callback,
            "/authorize": self.handle_authorize_endpoint,
            "/.well-known/oauth-authorization-server": self.handle_discovery_endpoint,
        }
        
        logger.info(colored(f"{server_name} initialization complete", "green"))
    
    def _initialize_components(self):
        """Initialize common components used by the server."""
        try:
            self.search_engine = SearchEngine()
            self.web_scraper = WebScraper()
            self.knowledge_base = KnowledgeBase()
            self.finnhub_api = FinnhubAPI()
            self.groundx_api = GroundXAPI()
            
            self.default_pdf_tool = MarkdownToPdfTool()
            self.pandoc_pdf_tool = None
            
            if PANDOC_AVAILABLE:
                try:
                    self.pandoc_pdf_tool = PandocMarkdownToPdfTool()
                    logger.info(colored("Pandoc PDF conversion available", "green"))
                except Exception as e:
                    logger.warning(f"Failed to initialize Pandoc PDF tool: {str(e)}")
            else:
                logger.info(colored("Pandoc not available, only default PDF conversion will be used", "yellow"))
                
            logger.info(colored("All components initialized successfully", "green"))
        except Exception as e:
            logger.error(f"Failed to initialize components: {str(e)}")
            raise
    
    def _register_handlers(self):
        """Register common handlers with the server."""
        @self.app.set_logging_level()
        async def handle_set_logging_level(level: types.LoggingLevel) -> None:
            await set_logging_level(self.app, level)
        
        @self.app.list_tools()
        async def handle_list_tools() -> list[types.Tool]:
            """List available tools."""
            logger.info(colored(f"{self.server_name.upper()}: Listing available tools", "green"))
            return get_tool_definitions()
        
        @self.app.call_tool()
        async def handle_tool_call(
            name: str, arguments: dict | None
        ) -> list[types.TextContent | types.ImageContent | types.EmbeddedResource]:
            """Handle tool execution requests."""
            return await handle_call_tool(
                name=name,
                arguments=arguments,
                search_engine=self.search_engine,
                web_scraper=self.web_scraper,
                knowledge_base=self.knowledge_base,
                default_pdf_tool=self.default_pdf_tool,
                pandoc_pdf_tool=self.pandoc_pdf_tool,
                finnhub_api=self.finnhub_api,
                groundx_api=self.groundx_api,
                server_context=self.app
            )
        
        @self.app.list_resources()
        async def resources_handler() -> list[types.Resource]:
            return await handle_list_resources()
        
        @self.app.read_resource()
        async def resource_read_handler(uri: types.AnyUrl) -> str:
            return await handle_read_resource(uri)
    
    def authenticate_request(self, scope):
        """Authenticate requests using OAuth tokens."""
        # Always accept any requests if DISABLE_ALL_AUTH is true
        if os.getenv("DISABLE_ALL_AUTH", "false").lower() == "true":
            logger.warning(colored(
                f"STREAM SERVER: Bypassing token validation due to DISABLE_ALL_AUTH=true", 
                "yellow"
            ))
            return True
            
        # Check for authorization header
        auth_header = None
        for name, value in scope.get("headers", []):
            if name == b"authorization":
                auth_header = value.decode("utf-8")
                break
        
        if not auth_header or not auth_header.lower().startswith("bearer "):
            logger.error(colored("STREAM SERVER: Missing or invalid authorization header", "red"))
            return False
            
        token = auth_header[7:]  # Skip "Bearer " prefix
        logger.info(colored(f"STREAM SERVER: Received bearer token: {token[:10]}...", "cyan"))
        
        # Check if token is valid in OAuth provider
        access_token = self.oauth_provider.tokens.get(token)
        if not access_token:
            logger.warning(colored(f"STREAM SERVER: Token not found: {token[:10]}...", "yellow"))
            return False
            
        # Check if expired
        if access_token.expires_at and access_token.expires_at < time.time():
            logger.warning(colored(f"STREAM SERVER: Token expired: {token[:10]}...", "yellow"))
            return False
            
        logger.info(colored(f"STREAM SERVER: Token validated", "green"))
        return True
    
    # OAuth endpoint handlers
    async def handle_token_endpoint(self, scope, receive, send, cors_headers):
        """Handle token endpoint."""
        logger.info(colored("STREAM SERVER: Token endpoint called", "green"))
        
        # Extract request body for token exchange
        body = b""
        more_body = True
        while more_body:
            message = await receive()
            body += message.get("body", b"")
            more_body = message.get("more_body", False)
        
        # Parse request body
        try:
            if body:
                body_str = body.decode("utf-8")
                token_request = dict(urllib.parse.parse_qsl(body_str))
            else:
                token_request = {}
            
            logger.debug(f"Token request: {token_request}")
        except Exception as e:
            logger.error(colored(f"Failed to parse token request: {str(e)}", "red"))
            await send({
                "type": "http.response.start",
                "status": 400,
                "headers": cors_headers + [(b"content-type", b"text/plain")],
            })
            await send({
                "type": "http.response.body",
                "body": b"Invalid request format",
            })
            return True
        
        # Handle token request based on grant type
        grant_type = token_request.get("grant_type")
        
        if grant_type == "authorization_code":
            # Exchange authorization code for token
            code = token_request.get("code")
            redirect_uri = token_request.get("redirect_uri")
            client_id = token_request.get("client_id", CLAUDE_CLIENT_ID)
            code_verifier = token_request.get("code_verifier")
            
            logger.info(colored(f"Processing authorization_code token request with code: {code[:10] if code else 'None'}", "green"))
            
            # Always generate a token for any valid-looking requests
            mcp_token = f"mcp_{secrets.token_hex(32)}"
            refresh_token = f"refresh_{secrets.token_hex(16)}"
            
            # Store token in OAuth provider
            token = AccessToken(
                token=mcp_token,
                client_id=client_id,
                scopes=["claudeai", "read", "write", "profile", "admin"],
                expires_at=int(time.time()) + 2592000,  # 30 days
            )
            self.oauth_provider.tokens[mcp_token] = token
            
            # Also store in token bridge for cross-component access
            self.oauth_provider.token_bridge[mcp_token] = {
                "client_id": client_id,
                "scopes": ["claudeai", "read", "write", "profile", "admin"],
                "created_at": datetime.now(),
            }
            
            # Format token response
            response_body = json.dumps({
                "access_token": mcp_token,
                "token_type": "bearer",
                "expires_in": 2592000, # 30 days
                "scope": "claudeai read write profile admin",
                "refresh_token": refresh_token
            }).encode("utf-8")
            
            # Add content-type to headers
            content_headers = cors_headers + [(b"content-type", b"application/json")]
            
            # Send response
            await send({
                "type": "http.response.start",
                "status": 200,
                "headers": content_headers,
            })
            await send({
                "type": "http.response.body",
                "body": response_body,
            })
            return True
            
        elif grant_type == "refresh_token":
            # Handle refresh token
            refresh_token = token_request.get("refresh_token")
            client_id = token_request.get("client_id", CLAUDE_CLIENT_ID)
            
            # Always generate a new token
            mcp_token = f"mcp_{secrets.token_hex(32)}"
            
            # Store token in OAuth provider
            token = AccessToken(
                token=mcp_token,
                client_id=client_id,
                scopes=["claudeai", "read", "write", "profile", "admin"],
                expires_at=int(time.time()) + 2592000,  # 30 days
            )
            self.oauth_provider.tokens[mcp_token] = token
            
            # Also store in token bridge for cross-component access
            self.oauth_provider.token_bridge[mcp_token] = {
                "client_id": client_id,
                "scopes": ["claudeai", "read", "write", "profile", "admin"],
                "created_at": datetime.now(),
            }
            
            # Format token response
            response_body = json.dumps({
                "access_token": mcp_token,
                "token_type": "bearer",
                "expires_in": 2592000, # 30 days
                "scope": "claudeai read write profile admin",
                "refresh_token": refresh_token  # Keep the same refresh token
            }).encode("utf-8")
            
            # Add content-type to headers
            content_headers = cors_headers + [(b"content-type", b"application/json")]
            
            # Send response
            await send({
                "type": "http.response.start",
                "status": 200,
                "headers": content_headers,
            })
            await send({
                "type": "http.response.body",
                "body": response_body,
            })
            return True
            
        else:
            # Unsupported grant type
            logger.error(colored(f"Unsupported grant type: {grant_type}", "red"))
            await send({
                "type": "http.response.start",
                "status": 400,
                "headers": cors_headers + [(b"content-type", b"text/plain")],
            })
            await send({
                "type": "http.response.body",
                "body": b"Unsupported grant type",
            })
            return True
    
    async def handle_oauth_callback(self, scope, receive, send, cors_headers):
        """Handle OAuth callback."""
        logger.info(colored("STREAM SERVER: OAuth callback endpoint called", "green"))
        
        # Extract query parameters
        query_params = {}
        if scope.get("query_string"):
            query_string = scope["query_string"].decode("utf-8")
            query_params = dict(urllib.parse.parse_qsl(query_string))
        
        # Log parameters for debugging
        logger.info(colored(f"STREAM SERVER: Callback parameters: {query_params}", "cyan"))
        
        # Handle Google OAuth callback
        state = query_params.get("state")
        code = query_params.get("code")
        error = query_params.get("error")
        
        # Check for errors
        if error:
            logger.error(colored(f"Google OAuth error: {error}", "red"))
            await send({
                "type": "http.response.start",
                "status": 400,
                "headers": cors_headers + [(b"content-type", b"text/plain")],
            })
            await send({
                "type": "http.response.body",
                "body": f"Google OAuth error: {error}".encode("utf-8"),
            })
            return True
        
        # Check required parameters
        if not state or not code:
            logger.error(colored("Missing state or code parameter", "red"))
            await send({
                "type": "http.response.start",
                "status": 400,
                "headers": cors_headers + [(b"content-type", b"text/plain")],
            })
            await send({
                "type": "http.response.body",
                "body": b"Missing state or code parameter",
            })
            return True
        
        # Load the original auth params from state
        original_auth_data = self.oauth_provider.auth_codes.get(state)
        if not original_auth_data:
            logger.error(colored(f"Invalid or expired state: {state}", "red"))
            await send({
                "type": "http.response.start",
                "status": 400,
                "headers": cors_headers + [(b"content-type", b"text/plain")],
            })
            await send({
                "type": "http.response.body",
                "body": b"Invalid or expired state",
            })
            return True
        
        # Extract original client information
        original_client_id = original_auth_data.client_id
        original_client_redirect_uri = original_auth_data.redirect_uri
        original_scopes = original_auth_data.scopes
        
        # Safely get code challenge information with fallbacks
        original_code_challenge = getattr(original_auth_data, 'code_challenge', None)
        original_code_challenge_method = getattr(original_auth_data, 'code_challenge_method', 'S256') if original_code_challenge else None
        
        # Exchange code for Google tokens
        google_client_id = os.getenv("GOOGLE_OAUTH_CLIENT_ID")
        google_client_secret = os.getenv("GOOGLE_OAUTH_CLIENT_SECRET")
        google_redirect_uri = os.getenv("GOOGLE_OAUTH_REDIRECT_URI")
        
        if not google_client_id or not google_client_secret or not google_redirect_uri:
            logger.error(colored("Google OAuth environment variables not set", "red"))
            await send({
                "type": "http.response.start",
                "status": 500,
                "headers": cors_headers + [(b"content-type", b"text/plain")],
            })
            await send({
                "type": "http.response.body",
                "body": b"Google OAuth environment variables not set",
            })
            return True
        
        # Exchange code for token
        token_req_data = {
            "code": code,
            "client_id": google_client_id,
            "client_secret": google_client_secret,
            "redirect_uri": google_redirect_uri,
            "grant_type": "authorization_code"
        }
        
        try:
            token_response = requests.post(GOOGLE_TOKEN_URL, data=token_req_data)
            token_response.raise_for_status()
            google_tokens = token_response.json()
            logger.info(colored(f"Google OAuth token exchange successful", "green"))
        except requests.exceptions.RequestException as e:
            logger.error(colored(f"Failed to exchange Google code for token: {str(e)}", "red"))
            await send({
                "type": "http.response.start",
                "status": 500,
                "headers": cors_headers + [(b"content-type", b"text/plain")],
            })
            await send({
                "type": "http.response.body",
                "body": b"Failed to exchange Google code for token",
            })
            return True
        except Exception as e:
            logger.error(colored(f"Google token exchange request failed: {str(e)}", "red"))
            await send({
                "type": "http.response.start",
                "status": 500,
                "headers": cors_headers + [(b"content-type", b"text/plain")],
            })
            await send({
                "type": "http.response.body",
                "body": b"Google token exchange request failed",
            })
            return True

        google_access_token = google_tokens.get("access_token")
        google_refresh_token = google_tokens.get("refresh_token") # May not always be present
        google_id_token = google_tokens.get("id_token") # Contains user info

        logger.info(colored(f"Google tokens received. Access token: {google_access_token[:10]}... ID token: {google_id_token[:10]}...", "green"))

        user_email = None
        if google_id_token and google_client_id:
            id_info = await self.oauth_provider.validate_google_id_token(google_id_token, google_client_id)
            if id_info:
                user_email = id_info.get("email")
                logger.info(colored(f"Authenticated Google User Email: {user_email}", "magenta"))
                
                allowed_emails_str = os.getenv("ALLOWED_GOOGLE_EMAIL")
                if allowed_emails_str and user_email:
                    # Split by comma and strip whitespace
                    allowed_emails = [email.strip().lower() for email in allowed_emails_str.split(',')]
                    if user_email.lower() not in allowed_emails:
                        logger.error(colored(f"Google user {user_email} is not in the allowed users list ({allowed_emails_str}). Access denied.", "red"))
                        await send({"type": "http.response.start", "status": 403, "headers": cors_headers + [(b"content-type", b"text/plain")]})
                        await send({"type": "http.response.body", "body": b"Access denied. User not authorized."})
                        return True
                    else:
                        logger.info(colored(f"Google user {user_email} is in the allowed users list. Access granted.", "green"))
                else:
                    logger.info(colored(f"No specific ALLOWED_GOOGLE_EMAIL set, user {user_email} access granted by default.", "yellow"))
            else:
                logger.error(colored("Failed to validate Google ID token or extract email.", "red"))
                await send({"type": "http.response.start", "status": 401, "headers": cors_headers + [(b"content-type", b"text/plain")]})
                await send({"type": "http.response.body", "body": b"Failed to validate Google identity."})
                return True
        else:
            logger.warning(colored("Google ID token or Google Client ID not available for validation.", "yellow"))
        
        # Generate our server's (MCP) authorization code
        mcp_auth_code_str = f"mcp_code_{secrets.token_hex(16)}"
        mcp_auth_code = AuthorizationCode(
            code=mcp_auth_code_str,
            client_id=original_client_id,
            redirect_uri=AnyHttpUrl(str(original_client_redirect_uri)),
            redirect_uri_provided_explicitly=True,
            expires_at=time.time() + 600,  # 10 minutes for our code
            scopes=original_scopes,
            code_challenge=original_code_challenge,
            code_challenge_method=original_code_challenge_method,
        )
        
        # Store MCP code in OAuth provider
        self.oauth_provider.auth_codes[mcp_auth_code_str] = mcp_auth_code
        
        # Store Google-specific information
        google_info = {
            'google_access_token': google_access_token,
            'google_refresh_token': google_refresh_token,
            'google_id_token': google_id_token
        }
        if user_email:
            google_info['google_user_email'] = user_email
            
        # Store this information in the OAuth provider's state
        if not hasattr(self.oauth_provider, 'google_auth_info'):
            self.oauth_provider.google_auth_info = {}
        self.oauth_provider.google_auth_info[mcp_auth_code_str] = google_info
        
        # Redirect back to the original client with our code
        redirect_url = construct_redirect_uri(
            str(original_client_redirect_uri),
            code=mcp_auth_code_str,
            state=state
        )
        
        logger.info(colored(f"Redirecting to original client with MCP code: {redirect_url}", "green"))
        
        # Send redirect response
        await send({
            "type": "http.response.start",
            "status": 302,
            "headers": cors_headers + [(b"location", redirect_url.encode("utf-8"))],
        })
        await send({
            "type": "http.response.body",
            "body": b"",
        })
        return True
    
    async def handle_authorize_endpoint(self, scope, receive, send, cors_headers):
        """Handle authorization endpoint."""
        logger.info(colored("STREAM SERVER: Authorize endpoint called", "green"))
        
        # Extract query parameters
        query_params = {}
        if scope.get("query_string"):
            query_string = scope["query_string"].decode("utf-8")
            query_params = dict(urllib.parse.parse_qsl(query_string))
        
        # Log parameters for debugging
        logger.info(colored(f"STREAM SERVER: Authorize parameters: {query_params}", "cyan"))
        
        # Extract required parameters
        client_id = query_params.get("client_id", CLAUDE_CLIENT_ID)
        redirect_uri = query_params.get("redirect_uri")
        state = query_params.get("state")
        response_type = query_params.get("response_type")
        scope_str = query_params.get("scope", "")
        code_challenge = query_params.get("code_challenge")
        code_challenge_method = query_params.get("code_challenge_method", "S256")
        
        # Minimal validation
        if not redirect_uri:
            logger.error(colored("Missing redirect_uri parameter", "red"))
            await send({
                "type": "http.response.start",
                "status": 400,
                "headers": cors_headers + [(b"content-type", b"text/plain")],
            })
            await send({
                "type": "http.response.body",
                "body": b"Missing redirect_uri parameter",
            })
            return True
        
        # Create AuthorizationParams 
        auth_params = AuthorizationParams(
            response_type=response_type or "code",
            redirect_uri=AnyUrl(redirect_uri),
            redirect_uri_provided_explicitly=True,
            client_id=client_id,
            state=state,
            scopes=scope_str.split() if scope_str else [],
            code_challenge=code_challenge,
            code_challenge_method=code_challenge_method if code_challenge else None,
        )
        
        # Use our OAuth provider to get redirect URL to Google
        try:
            # Create a dummy client info for this request
            client_info = OAuthClientInformationFull(
                client_id=client_id,
                client_secret="dummy-secret",
                redirect_uris=[redirect_uri],
                scopes=scope_str.split() if scope_str else [],
            )
            
            # Get redirect URL to Google's auth page
            redirect_url = await self.oauth_provider.authorize(client_info, auth_params)
            
            # Send redirect response
            await send({
                "type": "http.response.start",
                "status": 302,
                "headers": cors_headers + [(b"location", redirect_url.encode("utf-8"))],
            })
            await send({
                "type": "http.response.body",
                "body": b"",
            })
            return True
            
        except Exception as e:
            logger.error(colored(f"Error in authorize endpoint: {str(e)}", "red"))
            await send({
                "type": "http.response.start",
                "status": 500,
                "headers": cors_headers + [(b"content-type", b"text/plain")],
            })
            await send({
                "type": "http.response.body",
                "body": f"Authorization error: {str(e)}".encode("utf-8"),
            })
            return True
    
    async def handle_discovery_endpoint(self, scope, receive, send, cors_headers):
        """Handle OAuth 2.0 discovery endpoint."""
        logger.info(colored("STREAM SERVER: OAuth discovery endpoint called", "green"))
        
        # Get server URL from environment or default to localhost
        port = os.getenv("SERVER_PORT", "8511")
        server_url = os.getenv("SERVER_URL", f"http://localhost:{port}")
        
        # Format discovery document
        discovery_data = {
            "issuer": server_url,
            "authorization_endpoint": f"{server_url}/authorize",
            "token_endpoint": f"{server_url}/token",
            "token_endpoint_auth_methods_supported": ["client_secret_basic", "client_secret_post", "none"],
            "scopes_supported": ["claudeai", "read", "write", "profile", "admin"],
            "response_types_supported": ["code"],
            "grant_types_supported": ["authorization_code", "refresh_token"],
            "revocation_endpoint": f"{server_url}/revoke",
            "service_documentation": f"{server_url}/docs",
        }
        
        await send({
            "type": "http.response.start",
            "status": 200,
            "headers": cors_headers + [(b"content-type", b"application/json")],
        })
        await send({
            "type": "http.response.body",
            "body": json.dumps(discovery_data).encode(),
        })
        return True
    
    async def handle_custom_endpoint(self, path, method, scope, receive, send, cors_headers):
        """Handle OAuth and custom endpoints."""
        logger.info(colored(f"STREAM SERVER: Handling request for path: {path}, method: {method}", "cyan"))
        
        # Check if this is an OAuth endpoint
        handler = self.oauth_endpoints.get(path)
        if handler:
            logger.info(colored(f"STREAM SERVER: Using registered handler for OAuth endpoint: {path}", "green"))
            return await handler(scope, receive, send, cors_headers)
        
        # Handle health endpoint
        if path == "/health":
            logger.info(colored(f"STREAM SERVER: Handling health endpoint", "green"))
            health_data = await self.get_health_data()
            await send({
                "type": "http.response.start",
                "status": 200,
                "headers": cors_headers + [(b"content-type", b"application/json")],
            })
            await send({
                "type": "http.response.body",
                "body": json.dumps(health_data).encode(),
            })
            return True
        
        # If we got here, the endpoint was not handled
        logger.warning(colored(f"STREAM SERVER: No handler found for path: {path}", "yellow"))
        return False
    
    async def get_health_data(self):
        """Get server health data for healthcheck endpoint."""
        return {
            "server": self.server_name,
            "time": str(datetime.now()),
            "oauth_enabled": True,
            "token_count": len(self.oauth_provider.tokens),
            "auth_code_count": len(self.oauth_provider.auth_codes),
            "google_oauth_client_id": os.getenv("GOOGLE_OAUTH_CLIENT_ID", "not_set"),
            "google_oauth_redirect_uri": os.getenv("GOOGLE_OAUTH_REDIRECT_URI", "not_set"),
            "allowed_google_email": os.getenv("ALLOWED_GOOGLE_EMAIL", "not_set"),
            "transport": "StreamableHTTP",
        }
    
    def run_server(self, port=8511):
        """Run the server with StreamableHTTP transport."""
        # ASGI handler for all non-MCP endpoints (custom endpoints)
        async def handle_custom_http(scope, receive, send):
            if scope["type"] != "http":
                return
                
            # Extract path and method
            path = scope["path"]
            method = scope["method"]
            
            logger.info(colored(f"STREAM SERVER ROOT: Received request for path: {path}, method: {method}", "blue"))
            
            # Prepare CORS headers
            cors_headers = [
                (b"access-control-allow-origin", b"*"),
                (b"access-control-allow-methods", b"GET, POST, OPTIONS"),
                (b"access-control-allow-headers", b"authorization, content-type"),
                (b"access-control-max-age", b"3600"),
            ]
            
            # Handle OPTIONS preflight requests
            if method == "OPTIONS":
                logger.info(colored(f"STREAM SERVER ROOT: Handling OPTIONS request for {path}", "cyan"))
                await send({
                    "type": "http.response.start",
                    "status": 204,
                    "headers": cors_headers,
                })
                await send({
                    "type": "http.response.body",
                    "body": b"",
                })
                return
                
            # Handle OAuth and custom endpoints
            handled = await self.handle_custom_endpoint(path, method, scope, receive, send, cors_headers)
            
            # If not handled by a custom endpoint, return 404
            if not handled:
                logger.warning(colored(f"STREAM SERVER ROOT: No handler found for {path}, returning 404", "yellow"))
                await send({
                    "type": "http.response.start",
                    "status": 404,
                    "headers": cors_headers + [(b"content-type", b"text/plain")],
                })
                await send({
                    "type": "http.response.body",
                    "body": f"Endpoint not found: {path}".encode("utf-8"),
                })
        
        # This function is never used because we're mounting the handle_request method directly
        # Just keeping it for reference
        async def handle_streamable_http(scope, receive, send):
            # Let the session manager handle all MCP requests
            await self.session_manager.handle_request(scope, receive, send)
        
        # Create lifespan manager
        @contextlib.asynccontextmanager
        async def lifespan(app: Starlette) -> AsyncIterator[None]:
            """Lifespan for application startup and shutdown."""
            async with self.session_manager.run():
                logger.info(colored(f"StreamableHTTP server started on port {port}!", "green"))
                try:
                    yield
                finally:
                    logger.info(colored("StreamableHTTP server shutting down...", "yellow"))
        
        # Create a completely custom ASGI application that handles both MCP and OAuth endpoints
        async def app(scope, receive, send):
            """ASGI application that handles both StreamableHTTP and OAuth endpoints."""
            if scope["type"] != "http":
                # Handle non-HTTP requests
                return
                
            # Extract path and method
            path = scope["path"]
            method = scope["method"]
            
            # Log request details
            logger.info(colored(f"STREAM SERVER MASTER: Request for {method} {path}", "blue"))
            
            # Forward MCP requests to the StreamableHTTP handler
            if path.startswith("/mcp"):
                logger.info(colored(f"STREAM SERVER MASTER: Forwarding {method} {path} to StreamableHTTP handler", "cyan"))
                await self.session_manager.handle_request(scope, receive, send)
                return
            
            # Create a map of explicit endpoints we need to handle
            # For OAuth endpoints, we need to handle them directly with their own handlers
            if path == "/.well-known/oauth-authorization-server":
                # OAuth discovery endpoint
                cors_headers = [
                    (b"access-control-allow-origin", b"*"),
                    (b"access-control-allow-methods", b"GET, POST, OPTIONS"),
                    (b"access-control-allow-headers", b"authorization, content-type"),
                    (b"access-control-max-age", b"3600"),
                ]
                await self.handle_discovery_endpoint(scope, receive, send, cors_headers)
                return
            elif path == "/authorize":
                # OAuth authorize endpoint
                cors_headers = [
                    (b"access-control-allow-origin", b"*"),
                    (b"access-control-allow-methods", b"GET, POST, OPTIONS"),
                    (b"access-control-allow-headers", b"authorization, content-type"),
                    (b"access-control-max-age", b"3600"),
                ]
                await self.handle_authorize_endpoint(scope, receive, send, cors_headers)
                return
            elif path == "/token":
                # OAuth token endpoint
                cors_headers = [
                    (b"access-control-allow-origin", b"*"),
                    (b"access-control-allow-methods", b"GET, POST, OPTIONS"),
                    (b"access-control-allow-headers", b"authorization, content-type"),
                    (b"access-control-max-age", b"3600"),
                ]
                await self.handle_token_endpoint(scope, receive, send, cors_headers)
                return
            elif path == "/oauth/callback":
                # OAuth callback endpoint
                cors_headers = [
                    (b"access-control-allow-origin", b"*"),
                    (b"access-control-allow-methods", b"GET, POST, OPTIONS"),
                    (b"access-control-allow-headers", b"authorization, content-type"),
                    (b"access-control-max-age", b"3600"),
                ]
                await self.handle_oauth_callback(scope, receive, send, cors_headers)
                return
            elif path == "/health" or path == "/healthcheck":
                # Health check endpoint
                cors_headers = [
                    (b"access-control-allow-origin", b"*"),
                    (b"access-control-allow-methods", b"GET, POST, OPTIONS"),
                    (b"access-control-allow-headers", b"authorization, content-type"),
                    (b"access-control-max-age", b"3600"),
                ]
                health_data = await self.get_health_data()
                await send({
                    "type": "http.response.start",
                    "status": 200,
                    "headers": cors_headers + [(b"content-type", b"application/json")],
                })
                await send({
                    "type": "http.response.body",
                    "body": json.dumps(health_data).encode(),
                })
                return
            elif method == "OPTIONS":
                # Handle OPTIONS preflight requests
                cors_headers = [
                    (b"access-control-allow-origin", b"*"),
                    (b"access-control-allow-methods", b"GET, POST, OPTIONS"),
                    (b"access-control-allow-headers", b"authorization, content-type"),
                    (b"access-control-max-age", b"3600"),
                ]
                await send({
                    "type": "http.response.start",
                    "status": 204,
                    "headers": cors_headers,
                })
                await send({
                    "type": "http.response.body",
                    "body": b"",
                })
                return
            # Remove this elif block - we're now mounting the StreamableHTTP handler directly
            # so we don't need to handle /mcp paths in our custom app handler
            # This was causing the same request to be handled twice
            # Handle only non-MCP paths with our custom endpoints
            elif not path.startswith("/mcp"):
                # Handle any other path with custom endpoint handler
                cors_headers = [
                    (b"access-control-allow-origin", b"*"),
                    (b"access-control-allow-methods", b"GET, POST, OPTIONS"),
                    (b"access-control-allow-headers", b"authorization, content-type"),
                    (b"access-control-max-age", b"3600"),
                ]
                handled = await self.handle_custom_endpoint(path, method, scope, receive, send, cors_headers)
                
                # If not handled by custom endpoint handler, return 404
                if not handled:
                    logger.warning(colored(f"STREAM SERVER MASTER: No handler found for {path}", "yellow"))
                    await send({
                        "type": "http.response.start",
                        "status": 404,
                        "headers": cors_headers + [(b"content-type", b"text/plain")],
                    })
                    await send({
                        "type": "http.response.body",
                        "body": f"Endpoint not found: {path}".encode("utf-8"),
                    })
                    return
        
        # Run the server with our custom ASGI application
        logger.info(colored(f"Starting StreamableHTTP server on port {port}", "green"))
        
        # Use uvicorn but handle all requests through our app
        # Wrap the app with a lifespan handler instead of passing the lifespan function
        server_app = app
        
        # Just create a simple Starlette app with lifespan but let our custom app function handle all routes
        # Our custom app function will manually forward /mcp requests to the StreamableHTTP handler
        starlette_app = Starlette(
            debug=True,
            lifespan=lifespan,
            routes=[
                # Mount our custom app for all paths
                Mount("/", app=server_app)
            ]
        )
        
        uvicorn.run(
            starlette_app, 
            host="0.0.0.0", 
            port=port, 
            log_level="info",
        )

def create_google_oauth_stream_server():
    """Create a Google OAuth stream server."""
    return GoogleOAuthStreamServer()

def run_google_oauth_stream_server(port=8511):
    """Run a Google OAuth stream server with StreamableHTTP transport."""
    server = create_google_oauth_stream_server()
    server.run_server(port)

