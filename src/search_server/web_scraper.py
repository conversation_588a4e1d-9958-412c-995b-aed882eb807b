import aiohttp
import asyncio
import os
from typing import Optional, Dict, Any
from .utils.logging_utils import setup_component_logger

# Set up component-specific logger
logger = setup_component_logger("web_scraper", enable_rotation=True)

class WebScraper:
    def __init__(self):
        self.jina_api_key = os.getenv("JINA_API_KEY")
        if not self.jina_api_key:
            logger.error("JINA_API_KEY environment variable not set")
            raise ValueError("JINA_API_KEY environment variable not set")
            
        self.headers = {
            'Authorization': f'Bearer {self.jina_api_key}'
        }
        logger.info("WebScraper initialized successfully with JINA API key")

    async def scrape_url(self, url: str) -> Optional[Dict[str, Any]]:
        """
        Scrape content from a URL using Jina Reader API
        
        Args:
            url: The target URL to scrape
            
        Returns:
            Dict containing the scraped content or None if failed
        """
        try:
            jina_url = f'https://r.jina.ai/{url}'
            
            async with aiohttp.ClientSession() as session:
                async with session.get(jina_url, headers=self.headers, timeout=30) as response:
                    if response.status != 200:
                        logger.error(f"Failed to scrape URL {url}: HTTP {response.status}")
                        return {
                            'url': url,
                            'error': f"HTTP error {response.status}",
                            'status': 'failed'
                        }
                    
                    content = await response.text(encoding="utf-8")
                    return {
                        'url': url,
                        'content': content,
                        'status': 'success'
                    }
            
        except aiohttp.ClientError as e:
            logger.error(f"Failed to scrape URL {url}: {str(e)}")
            return {
                'url': url,
                'error': str(e),
                'status': 'failed'
            }
        except asyncio.TimeoutError:
            logger.error(f"Timeout while scraping URL {url}")
            return {
                'url': url,
                'error': "Request timed out after 30 seconds",
                'status': 'failed'
            }
        except Exception as e:
            logger.error(f"Unexpected error while scraping URL {url}: {str(e)}")
            return {
                'url': url,
                'error': f"Unexpected error: {str(e)}",
                'status': 'failed'
            } 