"""
Token bridge module to share auth tokens between OAuth provider and server implementations.
This ensures tokens generated by the OAuth flow are recognized by the SSE endpoint.
"""
from datetime import datetime
from typing import Dict, Any

# Global token storage shared between components
shared_authenticated_tokens: Dict[str, Dict[str, Any]] = {}

def register_token(token: str, client_id: str, user_id: str = None, scopes: list = None): # pyright: ignore[reportArgumentType]
    """
    Register a token in the shared token store.
    """
    shared_authenticated_tokens[token] = {
        "client_id": client_id,
        "created_at": datetime.now(),
        "user_id": user_id or "anonymous",
        "scope": " ".join(scopes) if scopes else "claudeai",
    }
    return True

def validate_token(token: str) -> bool:
    """
    Check if a token exists in the shared token store.
    """
    return token in shared_authenticated_tokens

def get_token_info(token: str) -> Dict[str, Any]:
    """
    Get token information if available.
    """
    return shared_authenticated_tokens.get(token, {})

def cleanup_expired_tokens(max_age_hours: int = 720): # 30 days (30*24=720 hours)
    """
    Remove expired tokens from the store.
    """
    now = datetime.now()
    expired_tokens = []
    
    for token, data in shared_authenticated_tokens.items():
        created_at = data.get("created_at")
        if created_at and (now - created_at).total_seconds() > max_age_hours * 3600:
            expired_tokens.append(token)
    
    for token in expired_tokens:
        shared_authenticated_tokens.pop(token, None)