import logging
import os
from termcolor import colored

# Create the logs directory if it doesn't exist
logs_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "logs")
os.makedirs(logs_dir, exist_ok=True)

# Set up logging
logger = logging.getLogger("search-server")
logger.setLevel(logging.INFO)

# Prevent logs from propagating to the root logger
logger.propagate = False

# Define common log format
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# Create console handler with colored output
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)

# Create custom formatter with colors for console output
class ColoredFormatter(logging.Formatter):
    COLORS = {
        'INFO': 'green',
        'WARNING': 'yellow',
        'ERROR': 'red',
        'DEBUG': 'blue',
        'CRITICAL': 'red'
    }

    def format(self, record):
        # Color the log level
        levelname = record.levelname
        if levelname in self.COLORS:
            record.levelname = colored(levelname, self.COLORS[levelname])
        
        # Color the message based on level
        msg = super().format(record)
        if record.levelno == logging.INFO:
            return colored(msg, 'green')
        elif record.levelno == logging.WARNING:
            return colored(msg, 'yellow')
        elif record.levelno == logging.ERROR:
            return colored(msg, 'red')
        elif record.levelno == logging.DEBUG:
            return colored(msg, 'blue')
        elif record.levelno == logging.CRITICAL:
            return colored(msg, 'red', attrs=['bold'])
        return msg

# Set up console handler with colored formatter
console_formatter = ColoredFormatter(LOG_FORMAT)
console_handler.setFormatter(console_formatter)

# Create file handler for logging to a file
log_file = os.path.join(logs_dir, "search_server.log")
file_handler = logging.FileHandler(log_file)
file_handler.setLevel(logging.INFO)

# Use a standard formatter for the file handler (no colors in log files)
file_formatter = logging.Formatter(LOG_FORMAT)
file_handler.setFormatter(file_formatter)

# Add handlers to logger if it doesn't already have handlers
if not logger.handlers:
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)
    print(f"Logging to console and file: {log_file}")