import os
import tempfile
import subprocess
from pathlib import Path
from termcolor import colored
import shutil

class PandocMarkdownToPdfTool:
    """Tool for converting markdown text to PDF files using Pandoc."""
    
    def __init__(self):
        """Initialize the markdown to PDF tool."""
        self.output_dir = self._get_output_directory()
        # 检查是否安装了pandoc
        self._check_dependencies()
        
    def _get_output_directory(self):
        """Get or create the output directory in the user's home directory."""
        home_dir = str(Path.home())
        output_dir = os.path.join(home_dir, '.search_server')
        
        # Create directory if it doesn't exist
        if not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir)
                print(colored(f"Created output directory: {output_dir}", "green"))
            except Exception as e:
                print(colored(f"Error creating output directory: {str(e)}", "red"))
                # Fallback to current directory if home directory is not accessible
                output_dir = os.path.join(os.getcwd(), '.search_server')
                if not os.path.exists(output_dir):
                    os.makedirs(output_dir)
                    
        return output_dir
    
    def _check_dependencies(self):
        """检查必要的依赖是否安装。"""
        # 检查pandoc
        try:
            subprocess.run(["pandoc", "--version"], capture_output=True, check=True)
            print(colored("✓ Pandoc is installed", "green"))
        except (subprocess.CalledProcessError, FileNotFoundError):
            print(colored("✗ Pandoc is not installed!", "red"))
            print(colored("  Please install pandoc:", "yellow"))
            print(colored("  - Ubuntu/Debian: sudo apt-get install pandoc", "yellow"))
            print(colored("  - CentOS/RHEL: sudo yum install pandoc", "yellow"))
            print(colored("  - macOS: brew install pandoc", "yellow"))
            print(colored("  - Windows: https://pandoc.org/installing.html", "yellow"))
            
        # 检查XeLaTeX
        try:
            subprocess.run(["xelatex", "--version"], capture_output=True, check=False)
            print(colored("✓ LaTeX (XeLaTeX) is installed", "green"))
        except FileNotFoundError:
            print(colored("✗ LaTeX (XeLaTeX) is not installed!", "red"))
            print(colored("  Without LaTeX, CJK characters may not display correctly in PDFs.", "red"))
            print(colored("  Please install XeLaTeX and required packages:", "yellow"))
            print(colored("  - Ubuntu/Debian:", "yellow"))
            print(colored("    sudo apt-get install texlive-xetex texlive-lang-chinese texlive-lang-japanese texlive-latex-recommended", "yellow"))
            print(colored("  - CentOS/RHEL:", "yellow"))
            print(colored("    sudo yum install texlive-xetex texlive-lang-chinese texlive-lang-japanese texlive-latex-recommended", "yellow"))
            print(colored("  - macOS:", "yellow"))
            print(colored("    brew install mactex", "yellow"))
            print(colored("  - Windows:", "yellow"))
            print(colored("    Install MiKTeX or TeX Live with CJK support", "yellow"))
            
        # 检查中文字体
        try:
            fonts = subprocess.run(["fc-list", ":lang=zh"], capture_output=True, text=True, check=False).stdout
            if "Noto Sans CJK SC" in fonts or "SimSun" in fonts or "WenQuanYi" in fonts:
                print(colored("✓ Chinese fonts are installed", "green"))
            else:
                print(colored("✗ No Chinese fonts found!", "red"))
                print(colored("  Please install Chinese fonts:", "yellow"))
                print(colored("  - Ubuntu/Debian: sudo apt-get install fonts-noto-cjk", "yellow"))
                print(colored("  - CentOS/RHEL: sudo yum install google-noto-cjk-fonts", "yellow"))
                print(colored("  - macOS: brew install font-noto-sans-cjk", "yellow"))
        except:
            print(colored("? Could not check for Chinese fonts", "yellow"))
    
    def _get_table_packages(self):
        """返回表格相关的LaTeX包。"""
        return r"""
% 表格相关包
\RequirePackage{array}
\RequirePackage{longtable}
\RequirePackage{booktabs}
\RequirePackage{multirow}
\RequirePackage{tabularx}

% 设置表格样式
\setlength{\LTcapwidth}{\textwidth}
\setlength{\LTleft}{0pt}
\setlength{\LTright}{0pt}
\setlength{\LTpre}{5pt}
\setlength{\LTpost}{5pt}
"""

    def convert_to_pdf(self, title, content, filename):
        """将Markdown内容转换为PDF文件。"""
        temp_files = []  # 用于跟踪需要清理的临时文件
        try:
            # 创建输出目录
            output_dir = self._get_output_directory()
            os.makedirs(output_dir, exist_ok=True)
            
            # 生成临时文件路径
            temp_md_path = os.path.join(output_dir, "temp_markdown.md")
            output_file = os.path.join(output_dir, filename)
            temp_files.append(temp_md_path)  # 添加到临时文件列表
            
            # 写入Markdown内容到临时文件
            with open(temp_md_path, "w", encoding="utf-8") as f:
                f.write(content)
            
            # 创建一个简单的header文件，只包含必要的表格包
            header_content = r"""
\usepackage{longtable}
\usepackage{array}
\usepackage{booktabs}
\usepackage{multirow}
\usepackage{xcolor}
\usepackage{graphicx}
\usepackage{xeCJK}

% CJK Language Support
\setCJKmainfont[
  Language=Chinese Simplified
]{Noto Sans CJK SC}

% Japanese Support
\newCJKfontfamily\jp[
  Language=Japanese
]{Noto Sans CJK JP}
"""
            header_path = os.path.join(output_dir, "header.tex")
            temp_files.append(header_path)  # 添加到临时文件列表
            with open(header_path, "w", encoding="utf-8") as f:
                f.write(header_content)
                
            # 检查依赖
            self._check_dependencies()
            
            # 尝试使用pandoc+xelatex转换
            try:
                # 首先尝试使用LaTeX引擎
                pdf_engine = self._find_pdf_engine()
                cmd = [
                    "pandoc",
                    temp_md_path,
                    "-o", output_file,
                    f"--pdf-engine={pdf_engine}",
                    "-V", "geometry:margin=1in",
                    "-V", "fontsize=11pt",
                    "-V", "mainfont=Noto Sans",
                    "-V", "CJKmainfont=Noto Sans CJK SC",
                    "-H", header_path,  # 使用简单的header文件
                    "--highlight-style=tango",
                    "--no-highlight",  # 禁用代码高亮
                    "--variable", "tables=true",
                    "--mathjax"
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True, check=True)
                
                print(colored("PDF generation completed successfully!", "green"))
                
                return {
                    "result": "successful",
                    "file_location": output_file
                }
                
            except subprocess.CalledProcessError as e:
                print(colored(f"Error during pandoc conversion: {e.stderr}", "red"))
                print(colored("Trying alternative method with wkhtmltopdf...", "yellow"))
                
                # 尝试使用wkhtmltopdf作为备选方案
                try:
                    # 创建一个HTML文件
                    temp_html_path = os.path.join(output_dir, "temp_markdown.html")
                    temp_files.append(temp_html_path)  # 添加到临时文件列表
                    
                    # 使用pandoc将markdown转换为HTML
                    html_cmd = [
                        "pandoc", 
                        temp_md_path, 
                        "-o", temp_html_path,
                        "--standalone",
                        "--metadata", f"title={title}"
                    ]
                    subprocess.run(html_cmd, check=True, capture_output=True)
                    
                    # 使用wkhtmltopdf将HTML转换为PDF
                    wk_cmd = [
                        "wkhtmltopdf",
                        "--encoding", "utf-8",
                        "--title", title,
                        "--margin-top", "20",
                        "--margin-right", "20",
                        "--margin-bottom", "20",
                        "--margin-left", "20",
                        temp_html_path, output_file
                    ]
                    subprocess.run(wk_cmd, check=True, capture_output=True)
                    
                    print(colored("PDF generation with wkhtmltopdf completed successfully!", "green"))
                    
                    return {
                        "result": "successful",
                        "file_location": output_file
                    }
                except Exception as wk_error:
                    print(colored(f"wkhtmltopdf conversion also failed: {str(wk_error)}", "red"))
                    raise Exception(f"PDF conversion failed with both methods: {e.stderr}")
                    
        except Exception as e:
            error_message = str(e)
            print(colored(f"Error occurred: {error_message}", "red"))
            
            return {
                "result": "failed",
                "file_location": "",
                "error": error_message
            }
        finally:
            # 清理所有临时文件
            print(colored("Cleaning up temporary files...", "blue"))
            for temp_file in temp_files:
                try:
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                        print(colored(f"Removed temporary file: {temp_file}", "blue"))
                except Exception as e:
                    print(colored(f"Failed to remove temporary file {temp_file}: {str(e)}", "yellow"))
    
    def _find_pdf_engine(self):
        """查找可用的PDF引擎。"""
        # 尝试多种PDF引擎
        engines = ["xelatex", "pdflatex", "lualatex"]
        
        for engine in engines:
            try:
                subprocess.run([engine, "--version"], 
                               capture_output=True, 
                               check=False)
                print(colored(f"Using {engine} for PDF generation", "green"))
                return engine
            except FileNotFoundError:
                continue
        
        print(colored("No LaTeX engine found. Will try wkhtmltopdf as fallback.", "yellow"))
        
        # 检查wkhtmltopdf是否可用
        try:
            subprocess.run(["wkhtmltopdf", "--version"], 
                           capture_output=True, 
                           check=False)
            print(colored("Using wkhtmltopdf as fallback", "yellow"))
            return "wkhtmltopdf"
        except FileNotFoundError:
            print(colored("wkhtmltopdf not found either.", "red"))
        
        # 如果找不到任何引擎，仍然返回xelatex，让pandoc处理错误
        print(colored("No PDF engine found. PDF generation may fail.", "red"))
        return "xelatex"
    
    def _get_latex_template(self):
        """返回自定义的LaTeX模板。"""
        return r"""
\documentclass[$if(fontsize)$$fontsize$,$endif$$if(papersize)$$papersize$,$endif$]{article}

% 使用更完整的宏包
\usepackage{iftex}
\usepackage{lmodern}

% 数学相关包
\usepackage{amsmath,amssymb,amsthm}

% 非必须但有用的包
\usepackage{graphicx}
\usepackage{xcolor}

% 代码相关包
\usepackage{fancyvrb}
\usepackage{listings}

% 必须在fontspec之前加载
\ifPDFTeX
  \usepackage[T1]{fontenc}
  \usepackage[utf8]{inputenc}
  \usepackage{textcomp}
\else
  % 字体相关设置
  \usepackage{fontspec}
  \usepackage{xeCJK}
  \defaultfontfeatures{Ligatures=TeX,Scale=1}
\fi

% CJK Language Support
\setCJKmainfont[
  Language=Chinese Simplified
]{Noto Sans CJK SC}

% Japanese Support
\newCJKfontfamily\jp[
  Language=Japanese
]{Noto Sans CJK JP}

% Fallback fonts
\newcommand{\backupCJK}{
  \setCJKmainfont[Language=Chinese Simplified]{SimSun}
  \newCJKfontfamily\jp[Language=Japanese]{IPAMincho}
}

\newcommand{\backupCJKtwo}{
  \setCJKmainfont[Language=Chinese Simplified]{WenQuanYi Micro Hei}
  \newCJKfontfamily\jp[Language=Japanese]{VL Gothic}
}

% 修复一些常见的Pandoc问题
\providecommand{\tightlist}{%
  \setlength{\itemsep}{0pt}\setlength{\parskip}{0pt}}

\usepackage{hyperref}
\hypersetup{
$if(title-meta)$
  pdftitle={$title-meta$},
$endif$
  colorlinks=true,
  linkcolor=blue,
  filecolor=blue,
  citecolor=blue,
  urlcolor=blue,
  breaklinks=true}

\linespread{1.1}

\begin{document}

$body$

\end{document}
""" 