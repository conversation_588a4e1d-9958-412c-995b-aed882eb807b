import aiohttp
import os
import asyncio
from typing import Dict, Any, List
from .utils.logging_utils import setup_component_logger

# Set up component-specific logger
logger = setup_component_logger("finnhub_api", enable_rotation=True)

class FinnhubAPI:
    """Tool for accessing financial data via Finnhub API."""
    
    def __init__(self):
        """Initialize the Finnhub API tool."""
        self.api_key = os.getenv("FINNHUB_API_KEY")
        self.base_url = "https://finnhub.io/api/v1"
        logger.info("Finnhub API tool initialized")
    
    async def symbol_lookup(self, query: str) -> Dict[str, Any]:
        """
        Search for best-matching symbols based on query.
        
        Args:
            query: Search term (symbol, name, ISIN, etc.)
            
        Returns:
            Dictionary with search results
        """
        endpoint = f"{self.base_url}/search"
        params = {
            "q": query,
            "token": self.api_key
        }
        
        try:
            logger.info(f"Looking up symbol: {query}")
            async with aiohttp.ClientSession() as session:
                async with session.get(endpoint, params=params, timeout=30) as response:
                    if response.status != 200:
                        logger.error(f"Error in symbol lookup: HTTP {response.status}")
                        return {
                            "error": f"HTTP error {response.status}",
                            "count": 0,
                            "result": []
                        }
                    
                    return await response.json()
        except asyncio.TimeoutError:
            logger.error(f"Timeout in symbol lookup for {query}")
            return {
                "error": "Request timed out after 30 seconds",
                "count": 0,
                "result": []
            }
        except Exception as e:
            logger.error(f"Error in symbol lookup: {str(e)}")
            return {
                "error": str(e),
                "count": 0,
                "result": []
            }
    
    async def company_news(self, symbol: str, from_date: str, to_date: str) -> List[Dict[str, Any]]:
        """
        Get company news for a specific symbol and date range.
        
        Args:
            symbol: Company symbol
            from_date: Start date in format YYYY-MM-DD
            to_date: End date in format YYYY-MM-DD
            
        Returns:
            List of news items
        """
        endpoint = f"{self.base_url}/company-news"
        params = {
            "symbol": symbol,
            "from": from_date,
            "to": to_date,
            "token": self.api_key
        }
        
        try:
            logger.info(f"Getting news for {symbol} from {from_date} to {to_date}")
            async with aiohttp.ClientSession() as session:
                async with session.get(endpoint, params=params, timeout=30) as response:
                    if response.status != 200:
                        logger.error(f"Error fetching company news: HTTP {response.status}")
                        return []
                    
                    return await response.json()
        except asyncio.TimeoutError:
            logger.error(f"Timeout fetching news for {symbol}")
            return []
        except Exception as e:
            logger.error(f"Error fetching company news: {str(e)}")
            return []
    
    async def sec_filings(self, 
                   symbol: str = None, 
                   cik: str = None, 
                   access_number: str = None,
                   form: str = None,
                   from_date: str = None,
                   to_date: str = None) -> List[Dict[str, Any]]:
        """
        Get SEC filings for a company.
        
        Args:
            symbol: Company symbol (optional)
            cik: CIK number (optional)
            access_number: Specific report access number (optional)
            form: Filter by form type (optional)
            from_date: Start date in format YYYY-MM-DD (optional)
            to_date: End date in format YYYY-MM-DD (optional)
            
        Returns:
            List of SEC filings
        """
        endpoint = f"{self.base_url}/stock/filings"
        params = {
            "token": self.api_key
        }
        
        # Add optional parameters if provided
        if symbol:
            params["symbol"] = symbol
        if cik:
            params["cik"] = cik
        if access_number:
            params["accessNumber"] = access_number
        if form:
            params["form"] = form
        if from_date:
            params["from"] = from_date
        if to_date:
            params["to"] = to_date
        
        try:
            logger.info(f"Getting SEC filings for parameters: {params}")
            async with aiohttp.ClientSession() as session:
                async with session.get(endpoint, params=params, timeout=30) as response:
                    if response.status != 200:
                        logger.error(f"Error fetching SEC filings: HTTP {response.status}")
                        return []
                    
                    return await response.json()
        except asyncio.TimeoutError:
            logger.error("Timeout fetching SEC filings")
            return []
        except Exception as e:
            logger.error(f"Error fetching SEC filings: {str(e)}")
            return [] 