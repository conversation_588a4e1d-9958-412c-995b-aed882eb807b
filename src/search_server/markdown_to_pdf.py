import os
import markdown
from termcolor import colored
from weasyprint import HTML, CSS
from weasyprint.text.fonts import FontConfiguration
import json
from pathlib import Path
import re
import base64
import tempfile
import subprocess
from urllib.parse import quote
from markdown.extensions.codehilite import CodeHiliteExtension
from markdown.extensions.tables import TableExtension

class MarkdownToPdfTool:
    """Tool for converting markdown text to PDF files."""
    
    def __init__(self):
        """Initialize the markdown to PDF tool."""
        self.output_dir = self._get_output_directory()
        
    def _get_output_directory(self):
        """Get or create the output directory in the user's home directory."""
        home_dir = str(Path.home())
        output_dir = os.path.join(home_dir, '.search_server')
        
        # Create directory if it doesn't exist
        if not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir)
                print(colored(f"Created output directory: {output_dir}", "green"))
            except Exception as e:
                print(colored(f"Error creating output directory: {str(e)}", "red"))
                # Fallback to current directory if home directory is not accessible
                output_dir = os.path.join(os.getcwd(), '.search_server')
                if not os.path.exists(output_dir):
                    os.makedirs(output_dir)
                    
        return output_dir
    
    def _process_latex(self, content):
        """
        Process LaTeX formulas in markdown content.
        
        Args:
            content (str): Markdown content with LaTeX formulas
            
        Returns:
            str: Processed content with LaTeX formulas prepared for rendering
        """
        # Create a custom HTML container for LaTeX formulas to isolate them from other styling
        def replace_block_formula(match):
            formula = match.group(1).strip()
            styled_formula = self._style_formula(formula, is_block=True)
            
            # Wrap in a completely isolated container
            return f'<div style="all: initial; display: block; font-family: \'STIX Two Math\', serif; text-align: center; margin: 1em 0; padding: 10px; color: black; background-color: #f9f9f9; border-radius: 3px;">{styled_formula}</div>'
        
        def replace_inline_formula(match):
            formula = match.group(1).strip()
            styled_formula = self._style_formula(formula, is_block=False)
            
            # Wrap in a completely isolated container
            return f'<span style="all: initial; display: inline; font-family: \'STIX Two Math\', serif; color: black; background-color: transparent;">{styled_formula}</span>'
        
        # Process block LaTeX formulas: $$formula$$
        content = re.sub(r'\$\$(.*?)\$\$', replace_block_formula, content, flags=re.DOTALL)
        
        # Process inline LaTeX formulas: $formula$
        # Negative lookbehind and lookahead to avoid matching $$ in block formulas
        content = re.sub(r'(?<!\$)\$(?!\$)(.+?)(?<!\$)\$(?!\$)', replace_inline_formula, content)
        
        return content
    
    def _style_formula(self, formula, is_block=False):
        """
        Apply styling to a LaTeX formula to make it look like properly rendered math.
        
        Args:
            formula (str): The LaTeX formula to style
            is_block (bool): Whether this is a block formula
            
        Returns:
            str: Plain text representation of the styled formula with Unicode symbols
        """
        # Remove any HTML tags that might be causing issues
        formula = re.sub(r'<[^>]*>', '', formula)
        
        # Create a LaTeX to plain text converter that uses Unicode symbols
        # This approach completely avoids HTML/CSS styling
        
        # First, pre-process some specific patterns that need special handling
        # Handle common LaTeX commands directly
        replacements = [
            # Basic operators
            ('\\cdot', '·'), ('\\pm', '±'), ('\\mp', '∓'),
            ('\\times', '×'), ('\\div', '÷'),
            ('\\exp', 'exp'),
            
            # Greek letters
            ('\\alpha', 'α'), ('\\beta', 'β'), ('\\gamma', 'γ'), ('\\delta', 'δ'),
            ('\\epsilon', 'ε'), ('\\varepsilon', 'ε'), ('\\zeta', 'ζ'), ('\\eta', 'η'),
            ('\\theta', 'θ'), ('\\vartheta', 'ϑ'), ('\\iota', 'ι'), ('\\kappa', 'κ'),
            ('\\lambda', 'λ'), ('\\mu', 'μ'), ('\\nu', 'ν'), ('\\xi', 'ξ'),
            ('\\pi', 'π'), ('\\varpi', 'ϖ'), ('\\rho', 'ρ'), ('\\varrho', 'ϱ'),
            ('\\sigma', 'σ'), ('\\varsigma', 'ς'), ('\\tau', 'τ'), ('\\upsilon', 'υ'),
            ('\\phi', 'φ'), ('\\varphi', 'φ'), ('\\chi', 'χ'), ('\\psi', 'ψ'),
            ('\\omega', 'ω'),
            
            # Capital Greek letters
            ('\\Gamma', 'Γ'), ('\\Delta', 'Δ'), ('\\Theta', 'Θ'), ('\\Lambda', 'Λ'),
            ('\\Xi', 'Ξ'), ('\\Pi', 'Π'), ('\\Sigma', 'Σ'), ('\\Upsilon', 'Υ'),
            ('\\Phi', 'Φ'), ('\\Psi', 'Ψ'), ('\\Omega', 'Ω'),
            
            # Brackets and delimiters
            ('\\langle', '⟨'), ('\\rangle', '⟩'),
            ('\\left(', '('), ('\\right)', ')'),
            ('\\left[', '['), ('\\right]', ']'),
            ('\\left{', '{'), ('\\right}', '}'),
            ('\\left\\langle', '⟨'), ('\\right\\rangle', '⟩'),
            
            # Norms
            ('\\|', '‖'),
            
            # Math alphabets
            ('\\mathbb{R}', 'ℝ'), ('\\mathbb{Z}', 'ℤ'), ('\\mathbb{N}', 'ℕ'),
            ('\\mathbb{Q}', 'ℚ'), ('\\mathbb{C}', 'ℂ'), ('\\mathbb{P}', 'ℙ'),
            
            # Calligraphic letters
            ('\\mathcal{A}', '𝒜'), ('\\mathcal{B}', 'ℬ'), ('\\mathcal{C}', '𝒞'),
            ('\\mathcal{D}', '𝒟'), ('\\mathcal{E}', 'ℰ'), ('\\mathcal{F}', 'ℱ'),
            ('\\mathcal{G}', '𝒢'), ('\\mathcal{H}', 'ℋ'), ('\\mathcal{I}', 'ℐ'),
            ('\\mathcal{J}', '𝒥'), ('\\mathcal{K}', '𝒦'), ('\\mathcal{L}', 'ℒ'),
            ('\\mathcal{M}', 'ℳ'), ('\\mathcal{N}', '𝒩'), ('\\mathcal{O}', '𝒪'),
            ('\\mathcal{P}', '𝒫'), ('\\mathcal{Q}', '𝒬'), ('\\mathcal{R}', 'ℛ'),
            ('\\mathcal{S}', '𝒮'), ('\\mathcal{T}', '𝒯'), ('\\mathcal{U}', '𝒰'),
            ('\\mathcal{V}', '𝒱'), ('\\mathcal{W}', '𝒲'), ('\\mathcal{X}', '𝒳'),
            ('\\mathcal{Y}', '𝒴'), ('\\mathcal{Z}', '𝒵'),
            
            # Common functions
            ('\\log', 'log'), ('\\ln', 'ln'), ('\\sin', 'sin'),
            ('\\cos', 'cos'), ('\\tan', 'tan'), ('\\cot', 'cot'), ('\\sec', 'sec'),
            ('\\csc', 'csc'), ('\\arcsin', 'arcsin'), ('\\arccos', 'arccos'),
            ('\\arctan', 'arctan'), ('\\sinh', 'sinh'), ('\\cosh', 'cosh'),
            ('\\tanh', 'tanh'), ('\\lim', 'lim'), ('\\max', 'max'), ('\\min', 'min'),
            
            # Operators and symbols
            ('\\cdot', '·'), ('\\ldots', '…'), ('\\dots', '…'), ('\\cdots', '⋯'),
            ('\\vdots', '⋮'), ('\\ddots', '⋱'), ('\\infty', '∞'),
            ('\\sum', '∑'), ('\\prod', '∏'), ('\\coprod', '∐'),
            ('\\int', '∫'), ('\\iint', '∬'), ('\\iiint', '∭'), ('\\oint', '∮'),
            ('\\partial', '∂'), ('\\nabla', '∇'), ('\\forall', '∀'),
            ('\\exists', '∃'), ('\\nexists', '∄'), ('\\in', '∈'), ('\\notin', '∉'),
            ('\\subset', '⊂'), ('\\supset', '⊃'), ('\\subseteq', '⊆'), ('\\supseteq', '⊇'),
            ('\\cup', '∪'), ('\\cap', '∩'), ('\\setminus', '∖'),
            ('\\emptyset', '∅'), ('\\varnothing', '∅'),
            
            # Logic symbols
            ('\\land', '∧'), ('\\wedge', '∧'), ('\\lor', '∨'), ('\\vee', '∨'),
            ('\\neg', '¬'), ('\\lnot', '¬'), ('\\to', '→'), ('\\implies', '⟹'),
            ('\\iff', '⟺'), ('\\top', '⊤'), ('\\bot', '⊥'),
            
            # Arrows and relations
            ('\\rightarrow', '→'), ('\\leftarrow', '←'), ('\\Rightarrow', '⇒'),
            ('\\Leftarrow', '⇐'), ('\\leftrightarrow', '↔'), ('\\Leftrightarrow', '⇔'),
            ('\\uparrow', '↑'), ('\\downarrow', '↓'), ('\\updownarrow', '↕'),
            ('\\mapsto', '↦'), ('\\longmapsto', '⟼'),
            ('\\approx', '≈'), ('\\sim', '∼'), ('\\simeq', '≃'), ('\\cong', '≅'),
            ('\\neq', '≠'), ('\\ne', '≠'), ('\\equiv', '≡'),
            ('\\leq', '≤'), ('\\geq', '≥'), ('\\ll', '≪'), ('\\gg', '≫'),
            ('\\prec', '≺'), ('\\succ', '≻'), ('\\preceq', '⪯'), ('\\succeq', '⪰'),
            
            # Space handling
            ('\\,', ' '), ('\\;', ' '), ('\\quad', '  '), ('\\qquad', '    '),
            
            # Special handling for environments
            ('\\\\', '\n'),
            ('&', ' '),
        ]
        
        # Process special LaTeX constructs that need custom handling
        # Process fractions
        def process_frac(match):
            num = match.group(1)
            denom = match.group(2)
            if len(num) == 1 and len(denom) == 1:
                # Try to use Unicode fraction if available
                if num == '1' and denom == '2':
                    return '½'
                elif num == '1' and denom == '4':
                    return '¼'
                elif num == '3' and denom == '4':
                    return '¾'
            # Otherwise return in simple format
            return f"{num}/{denom}"
        
        formula = re.sub(r'\\frac\{(.*?)\}\{(.*?)\}', process_frac, formula)
        
        # Process square roots
        def process_sqrt(match):
            content = match.group(1)
            return f"√{content}"
        
        formula = re.sub(r'\\sqrt\{(.*?)\}', process_sqrt, formula)
        
        # Process subscripts and superscripts
        def process_superscript(match):
            content = match.group(1)
            # Map to Unicode superscript if possible
            superscript_map = {
                '0': '⁰', '1': '¹', '2': '²', '3': '³', '4': '⁴',
                '5': '⁵', '6': '⁶', '7': '⁷', '8': '⁸', '9': '⁹',
                '+': '⁺', '-': '⁻', '=': '⁼', '(': '⁽', ')': '⁾',
                'n': 'ⁿ', 'i': 'ⁱ'
            }
            if len(content) == 1 and content in superscript_map:
                return superscript_map[content]
            return f"^{content}"
        
        def process_subscript(match):
            content = match.group(1)
            # Map to Unicode subscript if possible
            subscript_map = {
                '0': '₀', '1': '₁', '2': '₂', '3': '₃', '4': '₄',
                '5': '₅', '6': '₆', '7': '₇', '8': '₈', '9': '₉',
                '+': '₊', '-': '₋', '=': '₌', '(': '₍', ')': '₎',
                'a': 'ₐ', 'e': 'ₑ', 'o': 'ₒ', 'x': 'ₓ', 'h': 'ₕ',
                'k': 'ₖ', 'l': 'ₗ', 'm': 'ₘ', 'n': 'ₙ', 'p': 'ₚ',
                's': 'ₛ', 't': 'ₜ'
            }
            if len(content) == 1 and content in subscript_map:
                return subscript_map[content]
            return f"_{content}"
        
        formula = re.sub(r'\^{(.*?)}', process_superscript, formula)
        formula = re.sub(r'\^([0-9a-zA-Z])', process_superscript, formula)
        formula = re.sub(r'_{(.*?)}', process_subscript, formula)
        formula = re.sub(r'_([0-9a-zA-Z])', process_subscript, formula)
        
        # Apply all text replacements
        for latex_cmd, replacement in replacements:
            formula = formula.replace(latex_cmd, replacement)
        
        # Final cleanup
        formula = formula.replace('\\', '')  # Remove any remaining backslashes
        formula = re.sub(r'\s+', ' ', formula)  # Normalize whitespace
        formula = formula.strip()
        
        return formula
    
    def _process_align_environment(self, content):
        """Process align environment content."""
        lines = content.split('\\\\')
        processed_lines = []
        
        for line in lines:
            line = line.strip()
            if '&' in line:
                parts = line.split('&')
                if len(parts) >= 2:
                    left = parts[0].strip()
                    right = ''.join(parts[1:]).strip()
                    processed_line = f'{left} = {right}'
                else:
                    processed_line = line
            else:
                processed_line = line
            
            processed_lines.append(processed_line)
        
        return '\n'.join(processed_lines)
    
    def _process_cases_environment(self, content):
        """Process cases environment content."""
        lines = content.split('\\\\')
        processed_lines = []
        
        for line in lines:
            line = line.strip()
            if '&' in line:
                parts = line.split('&')
                if len(parts) >= 2:
                    left = parts[0].strip()
                    right = ''.join(parts[1:]).strip()
                    processed_line = f'{left}, {right}'
                else:
                    processed_line = line
            else:
                processed_line = line
            
            processed_lines.append(processed_line)
        
        return '{\n' + '\n'.join(processed_lines) + '\n}'
    
    def convert_to_pdf(self, title, content, filename):
        """
        Convert markdown content to PDF and save it.
        
        Args:
            title (str): Title to appear in the PDF header
            content (str): Markdown content to convert
            filename (str): Name of the output PDF file
            
        Returns:
            dict: Result of the conversion with status and file location
        """
        try:
            # Ensure filename ends with .pdf
            if not filename.lower().endswith('.pdf'):
                filename += '.pdf'
                
            # Create full output path
            output_file = os.path.join(self.output_dir, filename)
            
            print(colored(f"Converting markdown to PDF: {output_file}", "cyan"))
            
            # Process LaTeX formulas
            content = self._process_latex(content)
            
            # Set up markdown extensions
            extensions = [
                'tables',
                'fenced_code',
                'codehilite'
            ]
            
            # Convert markdown to HTML
            html_content = markdown.markdown(content, extensions=extensions)
            
            # Add CSS styling
            css = CSS(string='''
                @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC&display=swap');
                @import url('https://fonts.googleapis.com/css2?family=STIX+Two+Math&display=swap');
                
                /* 设置页面边距 */
                @page {
                    margin: 0.5cm 0.5cm;  /* 上下0.5cm，左右0.3cm */
                }
                
                body {
                    font-family: 'Noto Sans SC', sans-serif;
                    margin: 0;  /* 移除body边距，使用@page控制 */
                    padding: 0.3cm;  /* 添加少量内边距 */
                    line-height: 1.4;
                    color: #333;
                    font-size: 11px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 12px 0;
                    background: #fff;
                    font-size: 10px;
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 6px;
                    text-align: left;
                }
                th {
                    background-color: #f8f9fa;
                    font-weight: bold;
                }
                tr:nth-child(even) {
                    background-color: #f8f9fa;
                }
                h1, h2, h3, h4, h5, h6 {
                    color: #2c3e50;
                    margin-top: 0.5em;  /* 减少标题顶部边距 */
                    margin-bottom: 0.3em;
                    line-height: 1.2;
                }
                h1 {
                    text-align: center;
                    color: #2c3e50;
                    margin-top: 0;  /* 移除h1顶部边距 */
                    margin-bottom: 0.8em;
                    font-size: 20px;
                }
                h2 { font-size: 16px; }
                h3 { font-size: 14px; }
                p {
                    margin: 0.5em 0;
                    line-height: 1.4;
                }
                code {
                    background: #f8f9fa;
                    padding: 1px 3px;
                    border-radius: 2px;
                    font-family: monospace;
                    font-size: 10px;
                }
                pre {
                    background: #f8f9fa;
                    padding: 8px;
                    border-radius: 3px;
                    overflow-x: auto;
                    margin: 8px 0;
                }
                a {
                    color: #3498db;
                    text-decoration: none;
                }
                a:hover {
                    text-decoration: underline;
                }
                blockquote {
                    margin: 0.5em 0;
                    padding-left: 0.8em;
                    border-left: 3px solid #ddd;
                    color: #666;
                }
                img {
                    max-width: 100%;
                    height: auto;
                    display: block;
                    margin: 0.8em auto;
                }
                ul, ol {
                    margin: 0.5em 0;
                    padding-left: 1.5em;
                }
                li {
                    margin: 0.2em 0;
                }
            ''')

            
            # Create HTML with proper structure
            full_html = f'''
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>{title}</title>
                <style>
                    /* Reset all styles for math elements */
                    .math-formula * {{
                        all: initial;
                        font-family: 'STIX Two Math', serif;
                        color: #000;
                    }}
                </style>
            </head>
            <body>
                {html_content}
            </body>
            </html>
            '''
            
            # Generate PDF
            font_config = FontConfiguration()
            HTML(string=full_html).write_pdf(
                output_file,
                stylesheets=[css],
                font_config=font_config
            )
            
            print(colored("PDF generation completed successfully!", "green"))
            
            return {
                "result": "successful",
                "file_location": output_file
            }
            
        except Exception as e:
            error_message = str(e)
            print(colored(f"Error occurred: {error_message}", "red"))
            
            return {
                "result": "failed",
                "file_location": "",
                "error": error_message
            }

