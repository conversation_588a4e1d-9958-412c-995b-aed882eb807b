import os
from contextlib import asynccontextmanager

from mcp.server.fastmcp import FastMCP
from termcolor import colored

from .logger import logger

# Required environment variables
required_env_vars = [
    "TAVILY_API_KEY",
    "SERPER_API_KEY",
    "BING_API_KEY",
    "GOOGLE_API_KEY",
    "GOOGLE_SEARCH_ENGINE_ID",
    "JINA_API_KEY",
    "KNOWLEDGE_BASE_URL",
    "LINKUP_API_KEY",
    "EXA_API_KEY",
    "TXYZ_API_KEY",
    "FINNHUB_API_KEY",
    "FIRECRAWL_API_KEY",
    "GROUNDX_API_KEY",
    "OLLAMA_API_KEY",
]

# Optional StreamableHTTP specific environment variables
streamable_http_env_vars = ["STREAMABLE_HTTP_ENABLE", "STREAMABLE_HTTP_JSON_RESPONSE"]


def check_environment_variables():
    """Check if required environment variables are set."""
    # Check for required environment variables
    for var in required_env_vars:
        if not os.getenv(var):
            logger.warning(f"Missing optional environment variable: {var}")

    # Check for required OAuth variables if being enabled
    oauth_vars = [
        "GOOGLE_OAUTH_CLIENT_ID",
        "GOOGLE_OAUTH_CLIENT_SECRET",
        "GOOGLE_OAUTH_REDIRECT_URI",
    ]
    oauth_enabled = True

    for var in oauth_vars:
        if not os.getenv(var):
            logger.warning(f"Missing OAuth environment variable: {var}")
            oauth_enabled = False

    # Check for StreamableHTTP settings if enabled
    streamable_http_enabled = (
        os.getenv("STREAMABLE_HTTP_ENABLE", "false").lower() == "true"
    )
    if streamable_http_enabled:
        logger.info(colored("StreamableHTTP transport is enabled", "green"))

        # Log StreamableHTTP settings
        json_response = (
            os.getenv("STREAMABLE_HTTP_JSON_RESPONSE", "false").lower() == "true"
        )
        if json_response:
            logger.info(
                colored(
                    "StreamableHTTP will use JSON responses instead of SSE streams",
                    "cyan",
                )
            )
        else:
            logger.info(
                colored("StreamableHTTP will use SSE streaming for responses", "cyan")
            )

    return oauth_enabled


@asynccontextmanager
async def server_lifespan(app):
    """Server lifespan management."""
    logger.info(colored("Server lifespan starting...", "cyan"))
    yield
    logger.info(colored("Server shutting down", "yellow"))


def create_server(oauth_provider=None, auth_settings=None):
    """Create the FastMCP server instance."""
    if oauth_provider:
        logger.info(colored("Initializing FastMCP with OAuth support", "green"))
        server = FastMCP(
            "search_server", lifespan=server_lifespan, auth_provider=oauth_provider
        )
    else:
        logger.info(colored("Initializing FastMCP without OAuth support", "yellow"))
        server = FastMCP(
            "search_server",
            lifespan=server_lifespan,
        )

    # Store notes as a simple key-value dict to demonstrate state management
    server.notes = {}  # pyright: ignore[reportAttributeAccessIssue]

    return server
