"""
Direct MCP server implementation without OAuth.
This is used when DISABLE_ALL_AUTH=true to provide a simpler path
for testing tool visibility with <PERSON>.

This implementation uses the refactored base server component.
"""
import os
import asyncio
import json
from termcolor import colored
import mcp.server.stdio

from .logger import logger
from .utils.base_server import BaseSearchServer

class DirectServer(BaseSearchServer):
    """Direct server implementation without OAuth."""
    
    def __init__(self):
        """Initialize the direct server."""
        super().__init__(server_name="direct_server")
        logger.warning(colored("Creating DIRECT server without OAuth", "red"))
        
    def authenticate_request(self, scope):
        """Always allow authentication in direct server."""
        return True
    
    async def get_health_data(self):
        """Get server health data for healthcheck endpoint."""
        base_data = await super().get_health_data()
        base_data["auth_enabled"] = False
        return base_data
    
    # Standalone server entry point
    async def run_stdio_server(self):
        """Run the stdio direct server with error handling."""
        logger.info(colored("DIRECT SERVER: Starting stdio server", "green"))
        
        try:
            async with mcp.server.stdio.stdio_server() as streams:
                await self.server.run(
                    streams[0],
                    streams[1],
                    mcp.server.models.InitializationOptions(
                        server_name=self.server_name,
                        server_version="1.3.4",
                        capabilities=self.server.get_capabilities(
                            notification_options=mcp.server.NotificationOptions(),
                            experimental_capabilities={},
                        ),
                    ),
                )
        except Exception as e:
            # Create a proper MCP notification for the error
            error_message = {
                "jsonrpc": "2.0",
                "method": "notifications/error",
                "params": {
                    "code": -32603,  # Internal error code
                    "message": "Server error",
                    "data": {
                        "error": str(e)
                    }
                }
            }
            print(json.dumps(error_message))
            logger.error(colored(f"DIRECT SERVER: Error in stdio server: {str(e)}", "red"))
            raise

def create_direct_server():
    """Create a direct server without OAuth."""
    return DirectServer()


def run_direct_server(port=8511):
    """Run a direct server without OAuth."""
    server = create_direct_server()
    server.run_server(port)


async def run_stdio_direct_server():
    """Run a direct server with stdio transport."""
    server = create_direct_server()
    await server.run_stdio_server()