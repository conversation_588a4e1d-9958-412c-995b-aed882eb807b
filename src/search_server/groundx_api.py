import aiohttp
import os
import asyncio
import json
import base64
from typing import Dict, Any, List, Optional, Union
from termcolor import colored
from .logger import logger

class GroundXAPI:
    """Tool for accessing GroundX API for document management and search."""
    
    def __init__(self, timeout: int = 60):
        """
        Initialize the GroundX API tool.
        
        Args:
            timeout: Request timeout in seconds (default: 60)
        """
        self.api_key = os.getenv("GROUNDX_API_KEY")
        self.base_url = "https://api.groundx.ai/api/v1"
        self.timeout = timeout
        logger.info("GroundX API tool initialized")
        print(colored("GroundX API tool initialized", "green"))
    
    async def get_all_buckets(self) -> Dict[str, Any]:
        """
        List all buckets within the GroundX account.
        
        Returns:
            Dictionary with bucket information
        """
        endpoint = f"{self.base_url}/bucket"
        headers = {
            "X-API-Key": self.api_key
        }
        
        try:
            logger.info("Getting all buckets")
            print(colored("Getting all buckets", "cyan"))
            
            async with aiohttp.ClientSession() as session:
                async with session.get(endpoint, headers=headers, timeout=self.timeout) as response:
                    if response.status != 200:
                        error_msg = f"Error getting buckets: HTTP {response.status}"
                        logger.error(error_msg)
                        print(colored(error_msg, "red"))
                        return {
                            "error": error_msg,
                            "buckets": []
                        }
                    
                    return await response.json()
        except asyncio.TimeoutError:
            error_msg = f"Timeout getting buckets after {self.timeout} seconds"
            logger.error(error_msg)
            print(colored(error_msg, "red"))
            return {
                "error": error_msg,
                "buckets": []
            }
        except Exception as e:
            error_msg = f"Error getting buckets: {str(e)}"
            logger.error(error_msg)
            print(colored(error_msg, "red"))
            return {
                "error": error_msg,
                "buckets": []
            }
    
    async def get_bucket(self, bucket_id: int) -> Dict[str, Any]:
        """
        Look up a specific bucket by its bucketId.
        
        Args:
            bucket_id: ID of the bucket to retrieve
            
        Returns:
            Dictionary with bucket information
        """
        endpoint = f"{self.base_url}/bucket/{bucket_id}"
        headers = {
            "X-API-Key": self.api_key
        }
        
        try:
            logger.info(f"Getting bucket with ID: {bucket_id}")
            print(colored(f"Getting bucket with ID: {bucket_id}", "cyan"))
            
            async with aiohttp.ClientSession() as session:
                async with session.get(endpoint, headers=headers, timeout=self.timeout) as response:
                    if response.status != 200:
                        error_msg = f"Error getting bucket {bucket_id}: HTTP {response.status}"
                        logger.error(error_msg)
                        print(colored(error_msg, "red"))
                        return {
                            "error": error_msg
                        }
                    
                    return await response.json()
        except asyncio.TimeoutError:
            error_msg = f"Timeout getting bucket {bucket_id} after {self.timeout} seconds"
            logger.error(error_msg)
            print(colored(error_msg, "red"))
            return {
                "error": error_msg
            }
        except Exception as e:
            error_msg = f"Error getting bucket {bucket_id}: {str(e)}"
            logger.error(error_msg)
            print(colored(error_msg, "red"))
            return {
                "error": error_msg
            }
    
    async def create_bucket(self, name: str) -> Dict[str, Any]:
        """
        Create a new bucket in the GroundX account.
        
        Args:
            name: Name for the new bucket
            
        Returns:
            Dictionary with created bucket information
        """
        endpoint = f"{self.base_url}/bucket"
        headers = {
            "X-API-Key": self.api_key,
            "Content-Type": "application/json"
        }
        data = {
            "name": name
        }
        
        try:
            logger.info(f"Creating new bucket with name: {name}")
            print(colored(f"Creating new bucket with name: {name}", "cyan"))
            
            async with aiohttp.ClientSession() as session:
                async with session.post(endpoint, headers=headers, json=data, timeout=self.timeout) as response:
                    if response.status != 200:
                        error_msg = f"Error creating bucket: HTTP {response.status}"
                        logger.error(error_msg)
                        print(colored(error_msg, "red"))
                        return {
                            "error": error_msg
                        }
                    
                    return await response.json()
        except asyncio.TimeoutError:
            error_msg = f"Timeout creating bucket after {self.timeout} seconds"
            logger.error(error_msg)
            print(colored(error_msg, "red"))
            return {
                "error": error_msg
            }
        except Exception as e:
            error_msg = f"Error creating bucket: {str(e)}"
            logger.error(error_msg)
            print(colored(error_msg, "red"))
            return {
                "error": error_msg
            }
    
    async def delete_bucket(self, bucket_id: int) -> Dict[str, Any]:
        """
        Delete a bucket from the GroundX account.
        
        Args:
            bucket_id: ID of the bucket to delete
            
        Returns:
            Dictionary with deletion status
        """
        endpoint = f"{self.base_url}/bucket/{bucket_id}"
        headers = {
            "X-API-Key": self.api_key
        }
        
        try:
            logger.info(f"Deleting bucket with ID: {bucket_id}")
            print(colored(f"Deleting bucket with ID: {bucket_id}", "cyan"))
            
            async with aiohttp.ClientSession() as session:
                async with session.delete(endpoint, headers=headers, timeout=self.timeout) as response:
                    if response.status != 200:
                        error_msg = f"Error deleting bucket {bucket_id}: HTTP {response.status}"
                        logger.error(error_msg)
                        print(colored(error_msg, "red"))
                        return {
                            "error": error_msg
                        }
                    
                    return await response.json()
        except asyncio.TimeoutError:
            error_msg = f"Timeout deleting bucket {bucket_id} after {self.timeout} seconds"
            logger.error(error_msg)
            print(colored(error_msg, "red"))
            return {
                "error": error_msg
            }
        except Exception as e:
            error_msg = f"Error deleting bucket {bucket_id}: {str(e)}"
            logger.error(error_msg)
            print(colored(error_msg, "red"))
            return {
                "error": error_msg
            }
    
    async def search_documents(self, bucket_id: int, query: str, relevance: int = 10) -> Dict[str, Any]:
        """
        Search documents on GroundX for the most relevant information.
        
        Args:
            bucket_id: ID of the bucket to search in
            query: Search query string
            relevance: Number of relevant results to return (default: 10)
            
        Returns:
            Dictionary with search results
        """
        endpoint = f"{self.base_url}/search/{bucket_id}"
        headers = {
            "X-API-Key": self.api_key,
            "Content-Type": "application/json"
        }
        data = {
            "query": query,
            "relevance": relevance
        }
        
        try:
            logger.info(f"Searching in bucket {bucket_id} with query: {query}")
            print(colored(f"Searching in bucket {bucket_id} with query: {query}", "cyan"))
            
            async with aiohttp.ClientSession() as session:
                async with session.post(endpoint, headers=headers, json=data, timeout=self.timeout) as response:
                    if response.status != 200:
                        error_msg = f"Error searching documents: HTTP {response.status}"
                        logger.error(error_msg)
                        print(colored(error_msg, "red"))
                        return {
                            "error": error_msg,
                            "results": []
                        }
                    
                    return await response.json()
        except asyncio.TimeoutError:
            error_msg = f"Timeout searching documents after {self.timeout} seconds"
            logger.error(error_msg)
            print(colored(error_msg, "red"))
            return {
                "error": error_msg,
                "results": []
            }
        except Exception as e:
            error_msg = f"Error searching documents: {str(e)}"
            logger.error(error_msg)
            print(colored(error_msg, "red"))
            return {
                "error": error_msg,
                "results": []
            }
    
    async def upload_documents(self, bucket_id: int, files: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Upload documents to a GroundX bucket.
        
        Args:
            bucket_id: ID of the bucket to upload to
            files: List of file objects with file paths and metadata
                  Each file should have:
                  - file_path: Path to the file to upload
                  - fileName: Name of the file
                  - fileType: Type of file (e.g., 'pdf', 'txt')
            
        Returns:
            Dictionary with upload status
        """
        endpoint = f"{self.base_url}/ingest/documents/local"
        headers = {
            "X-API-Key": self.api_key
        }
        
        try:
            logger.info(f"Uploading {len(files)} documents to bucket {bucket_id}")
            print(colored(f"Uploading {len(files)} documents to bucket {bucket_id}", "cyan"))
            
            # Prepare form data for multipart/form-data request
            form_data = aiohttp.FormData()
            
            for file_info in files:
                file_path = file_info.get('file_path')
                if not file_path or not os.path.exists(file_path):
                    logger.error(f"File not found: {file_path}")
                    return {
                        "error": f"File not found: {file_path}"
                    }
                
                # Add the file directly using file reference approach
                # This works for both text and binary files like PDFs
                try:
                    form_data.add_field('blob', 
                                       open(file_path, 'rb'),
                                       filename=file_info['fileName'])
                except Exception as e:
                    logger.error(f"Error reading file {file_path}: {str(e)}")
                    return {
                        "error": f"Error reading file {file_path}: {str(e)}"
                    }
                
                # Add metadata
                metadata = {
                    "bucketId": bucket_id,
                    "fileName": file_info['fileName'],
                    "fileType": file_info['fileType']
                }
                form_data.add_field('metadata', json.dumps(metadata))
            
            async with aiohttp.ClientSession() as session:
                async with session.post(endpoint, headers=headers, data=form_data, timeout=self.timeout) as response:
                    if response.status != 200:
                        error_msg = f"Error uploading documents: HTTP {response.status}"
                        logger.error(error_msg)
                        print(colored(error_msg, "red"))
                        return {
                            "error": error_msg
                        }
                    
                    return await response.json()
        except asyncio.TimeoutError:
            error_msg = f"Timeout uploading documents after {self.timeout} seconds"
            logger.error(error_msg)
            print(colored(error_msg, "red"))
            return {
                "error": error_msg
            }
        except Exception as e:
            error_msg = f"Error uploading documents: {str(e)}"
            logger.error(error_msg)
            print(colored(error_msg, "red"))
            return {
                "error": error_msg
            }
    
    async def get_all_documents(self) -> Dict[str, Any]:
        """
        List all documents across all resources which are currently on GroundX.
        
        Returns:
            Dictionary with document information
        """
        endpoint = f"{self.base_url}/ingest/documents"
        headers = {
            "X-API-Key": self.api_key
        }
        
        try:
            logger.info("Getting all documents")
            print(colored("Getting all documents", "cyan"))
            
            async with aiohttp.ClientSession() as session:
                async with session.get(endpoint, headers=headers, timeout=self.timeout) as response:
                    if response.status != 200:
                        error_msg = f"Error getting documents: HTTP {response.status}"
                        logger.error(error_msg)
                        print(colored(error_msg, "red"))
                        return {
                            "error": error_msg,
                            "documents": []
                        }
                    
                    return await response.json()
        except asyncio.TimeoutError:
            error_msg = f"Timeout getting documents after {self.timeout} seconds"
            logger.error(error_msg)
            print(colored(error_msg, "red"))
            return {
                "error": error_msg,
                "documents": []
            }
        except Exception as e:
            error_msg = f"Error getting documents: {str(e)}"
            logger.error(error_msg)
            print(colored(error_msg, "red"))
            return {
                "error": error_msg,
                "documents": []
            } 