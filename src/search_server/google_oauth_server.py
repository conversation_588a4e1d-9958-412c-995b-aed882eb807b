"""
Google OAuth MCP server implementation.

This implementation combines the direct server pattern (which is proven to work with <PERSON> tools)
with Google OAuth authentication. It addresses several key issues:

1. Uses the direct server pattern from direct_server.py that correctly exposes tools to Claude
2. Implements complete OAuth compatibility with token generation and validation
3. <PERSON><PERSON> requests properly without using async patterns that can cause errors
4. Provides special handling for <PERSON> requests to ensure compatibility

This implementation uses the refactored base server component.
"""

import asyncio
import json
import os
import secrets
import socket
import time
import urllib.parse
from datetime import datetime
from typing import Any, Dict, List, Optional

import requests
from google.auth.transport import requests as google_auth_requests
# Add google-auth imports for ID token validation
from google.oauth2 import id_token as google_id_token_verifier
from mcp.server.auth.provider import (AccessToken, AuthorizationCode,
                                      AuthorizationParams,
                                      OAuthAuthorizationServerProvider,
                                      RefreshToken, construct_redirect_uri)
from mcp.shared.auth import OAuthClientInformationFull, OAuthToken
from pydantic import AnyHttpUrl, AnyUrl
from termcolor import colored

from .logger import logger
from .token_bridge import shared_authenticated_tokens
from .utils.base_server import BaseSearchServer

# Constants
# CLAUDE_CLIENT_ID = os.getenv("CLAUDE_CLIENT_ID", "********-ff92-4c28-b15f-32186412aacd")
CLAUDE_CLIENT_ID = os.getenv("CLAUDE_CLIENT_ID")
GOOGLE_AUTHORIZATION_URL = "https://accounts.google.com/o/oauth2/v2/auth"
GOOGLE_TOKEN_URL = "https://oauth2.googleapis.com/token"
GOOGLE_USERINFO_URL = "https://www.googleapis.com/oauth2/v3/userinfo"

# Note: Removed complex client IP detection - keeping localhost for simplicity


class GoogleOAuthProvider(OAuthAuthorizationServerProvider):
    """Google OAuth provider implementation for the server"""

    def __init__(self):
        self.clients: dict[str, OAuthClientInformationFull] = {}
        self.auth_codes: dict[str, AuthorizationCode] = {}
        self.tokens: dict[str, AccessToken] = {}
        self.refresh_tokens: dict[str, RefreshToken] = {}

        # Dictionary to store Google-specific auth information
        self.google_auth_info: dict[str, dict] = {}

        # Dictionary to store PKCE challenge methods (since AuthorizationCode doesn't have this field)
        self.pkce_challenge_methods: dict[str, str] = {}

        # Share tokens with the token bridge
        self.token_bridge = shared_authenticated_tokens

        logger.info(colored("GoogleOAuthProvider initialized successfully", "green"))

    async def get_client(self, client_id: str) -> Optional[OAuthClientInformationFull]:
        """Get OAuth client information."""
        logger.debug(f"Looking up client: {client_id}")
        return self.clients.get(client_id)

    async def register_client(self, client_info: OAuthClientInformationFull):
        """Register a new OAuth client."""
        try:
            logger.info(
                colored(
                    f"Starting client registration for ID: {client_info.client_id}",
                    "green",
                )
            )
            logger.debug(f"Client info object: {client_info}")

            # Validate client info
            if not client_info.client_id or not client_info.client_secret:
                raise ValueError("Client ID and secret are required")

            # Store the client
            self.clients[client_info.client_id] = client_info
            logger.info(
                colored(
                    f"Successfully registered client: {client_info.client_id}", "green"
                )
            )
            logger.debug(f"Current registered clients: {list(self.clients.keys())}")

        except Exception as e:
            logger.error(colored(f"Error registering client: {str(e)}", "red"))
            logger.error(f"Client info that caused error: {client_info}")
            raise  # Re-raise the exception after logging

    async def authorize(
        self, client: OAuthClientInformationFull, params: AuthorizationParams
    ) -> str:
        """Constructs the redirect URL to Google's authorization server."""
        google_client_id = os.getenv("GOOGLE_OAUTH_CLIENT_ID")
        google_redirect_uri = os.getenv("GOOGLE_OAUTH_REDIRECT_URI")

        if not google_client_id or not google_redirect_uri:
            logger.error(
                colored(
                    "Google OAuth environment variables (GOOGLE_OAUTH_CLIENT_ID, GOOGLE_OAUTH_REDIRECT_URI) not set",
                    "red",
                )
            )
            raise ValueError("Google OAuth environment variables not set")

        state = params.state or secrets.token_hex(16)
        # Store state to verify later in the callback. Using auth_codes dict for simplicity for now.
        # A more robust solution might use a dedicated state store with TTL.

        self.auth_codes[state] = AuthorizationCode(
            code=state,  # Using state as a temporary holder for itself
            client_id=client.client_id,  # This would be our app's client_id if we distinguish
            redirect_uri=params.redirect_uri,  # The original redirect_uri requested by client
            redirect_uri_provided_explicitly=True,  # Add this required field
            expires_at=time.time() + 300,  # 5 minutes for state validity
            scopes=params.scopes or [],
            code_challenge=params.code_challenge or "",  # Provide default empty string
        )

        # Store PKCE challenge method separately if provided
        if params.code_challenge:
            # Extract method from the code_challenge parameter or default to 'plain'
            # Note: The AuthorizationParams doesn't have code_challenge_method field
            # so this will be handled at the server level
            pass

        query_params = {
            "client_id": google_client_id,
            "redirect_uri": google_redirect_uri,  # This is OUR redirect_uri registered with Google
            "response_type": "code",
            "scope": "openid email profile",  # Standard Google scopes
            "state": state,
            "access_type": "offline",  # To get a refresh token
            "prompt": "consent",  # Ensures the consent screen is shown, good for testing
        }

        redirect_url = (
            f"{GOOGLE_AUTHORIZATION_URL}?{urllib.parse.urlencode(query_params)}"
        )

        logger.info(
            colored(f"Redirecting to Google for authorization: {redirect_url}", "blue")
        )
        return redirect_url

    async def load_authorization_code(
        self, client: OAuthClientInformationFull, authorization_code: str
    ) -> Optional[AuthorizationCode]:
        """Load an authorization code (our MCP code) or a stored state parameter."""
        # This method is used for two purposes in this modified flow:
        # 1. In /oauth/callback: to retrieve the original state, PKCE challenge.
        # 2. In /token: to retrieve our MCP authorization code for exchange.
        logger.debug(f"Loading authorization_code: {authorization_code[:10]}...")
        return self.auth_codes.get(authorization_code)

    async def exchange_authorization_code(
        self,
        client: OAuthClientInformationFull,
        authorization_code: AuthorizationCode,
        code_verifier: Optional[str] = None,
    ) -> OAuthToken:
        """Exchange our MCP authorization code for an MCP token. PKCE verification for non-Claude clients."""
        logger.info(
            colored(
                f"Exchanging MCP authorization code: {authorization_code.code[:10]}... for client {client.client_id}",
                "green",
            )
        )

        # PKCE verification if a challenge was associated with the MCP auth code
        # This happens if the original /authorize request from Claude included PKCE params.
        # Safely get code challenge with fallbacks
        code_challenge = getattr(authorization_code, "code_challenge", None)
        if code_challenge:
            # Get the stored challenge method
            challenge_method = self.pkce_challenge_methods.get(
                authorization_code.code, "plain"
            )

            if client.client_id == CLAUDE_CLIENT_ID:
                logger.info(
                    colored(
                        "Bypassing PKCE verification for Claude client on MCP code exchange",
                        "yellow",
                    )
                )
            elif not code_verifier:
                logger.error(
                    colored(
                        "PKCE verification required for MCP code but no code_verifier provided",
                        "red",
                    )
                )
                raise ValueError(
                    "PKCE verification required for MCP code but no code_verifier provided"
                )
            else:
                import base64
                import hashlib

                if challenge_method == "S256":
                    # S256 method: SHA256 hash of the verifier
                    verifier_bytes = code_verifier.encode("utf-8")
                    digest = hashlib.sha256(verifier_bytes).digest()
                    computed_challenge = (
                        base64.urlsafe_b64encode(digest).decode("utf-8").rstrip("=")
                    )
                elif challenge_method == "plain":
                    # Plain method: verifier is used as-is
                    computed_challenge = code_verifier
                else:
                    logger.error(
                        colored(
                            f"Unsupported PKCE challenge method: {challenge_method}",
                            "red",
                        )
                    )
                    raise ValueError(
                        f"Unsupported PKCE challenge method: {challenge_method}"
                    )

                if computed_challenge != code_challenge:
                    logger.error(
                        colored(
                            f"PKCE verification failed for MCP code (method: {challenge_method})",
                            "red",
                        )
                    )
                    raise ValueError("PKCE verification failed for MCP code")
                logger.info(
                    colored(
                        f"PKCE verification successful for MCP code (method: {challenge_method})",
                        "green",
                    )
                )

            # Clean up stored challenge method
            if authorization_code.code in self.pkce_challenge_methods:
                del self.pkce_challenge_methods[authorization_code.code]

        # Generate our MCP token
        mcp_token_str = f"mcp_{secrets.token_hex(32)}"

        # Store MCP token
        # The scopes here should be what our server grants, potentially based on Google scopes
        # For now, using the scopes from the MCP auth code (which were from original request)
        internal_token = AccessToken(
            token=mcp_token_str,
            client_id=client.client_id,  # The client that requested authorization (e.g. Claude)
            scopes=authorization_code.scopes,
            expires_at=int(time.time()) + 2592000,  # 30 days for MCP token
            # We would also store the Google tokens and user info associated with this MCP token here
            # e.g., authorization_code.google_access_token, authorization_code.google_user_email
        )
        self.tokens[mcp_token_str] = internal_token

        # Store in token bridge if needed (e.g., for user_id or specific attributes)
        token_info = {
            "client_id": client.client_id,
            "scopes": authorization_code.scopes,
            "created_at": datetime.now(),
        }

        # Add Google user info if available
        google_info = self.google_auth_info.get(authorization_code.code, {})
        if "google_user_email" in google_info:
            token_info["google_user"] = google_info["google_user_email"]

        self.token_bridge[mcp_token_str] = token_info

        # Create MCP refresh token (optional, can reuse Google's refresh if applicable)
        mcp_refresh_token_str = f"mcp_refresh_{secrets.token_hex(16)}"
        # Create refresh token data
        refresh_token_data = {
            "token": mcp_refresh_token_str,
            "client_id": client.client_id,
            "scopes": authorization_code.scopes,
        }

        # Add Google refresh token if available
        google_info = self.google_auth_info.get(authorization_code.code, {})
        if "google_refresh_token" in google_info:
            # We can't add this to the RefreshToken model, but we can store it separately
            # if needed for future token refreshes
            pass

        self.refresh_tokens[mcp_refresh_token_str] = RefreshToken(
            token=mcp_refresh_token_str,
            client_id=client.client_id,
            scopes=authorization_code.scopes,
        )

        # Remove used MCP authorization code
        if authorization_code.code in self.auth_codes:
            del self.auth_codes[authorization_code.code]

        # Clean up Google auth info if it exists
        google_auth_info = getattr(self, "google_auth_info", {})
        if authorization_code.code in google_auth_info:
            del google_auth_info[authorization_code.code]

        oauth_token_response = OAuthToken(
            access_token=mcp_token_str,
            token_type="Bearer",  # Fixed capitalization
            expires_in=2592000,  # 30 days
            scope=" ".join(authorization_code.scopes),
            refresh_token=mcp_refresh_token_str,
            # id_token can also be passed if we generate one for our domain
        )

        logger.info(colored(f"MCP Token issued: {mcp_token_str[:10]}...", "green"))
        return oauth_token_response

    async def load_access_token(self, token: str) -> Optional[AccessToken]:
        """Load and validate an access token."""
        access_token = self.tokens.get(token)
        if not access_token:
            logger.debug(f"Token not found: {token[:10]}...")
            return None

        # Check if expired
        if access_token.expires_at and access_token.expires_at < time.time():
            logger.debug(f"Token expired: {token[:10]}...")
            del self.tokens[token]
            return None

        logger.debug(f"Token validated: {token[:10]}...")
        return access_token

    async def load_refresh_token(
        self, client: OAuthClientInformationFull, refresh_token: str
    ) -> Optional[RefreshToken]:
        """Load a refresh token"""
        return self.refresh_tokens.get(refresh_token)

    async def exchange_refresh_token(
        self,
        client: OAuthClientInformationFull,
        refresh_token: RefreshToken,
        scopes: Optional[list[str]] = None,
    ) -> OAuthToken:
        """Exchange refresh token for new access token"""
        # Generate new token
        mcp_token = f"mcp_{secrets.token_hex(32)}"

        # Use original scopes if none provided
        token_scopes = scopes or refresh_token.scopes

        # Store token
        token = AccessToken(
            token=mcp_token,
            client_id=client.client_id,
            scopes=token_scopes,
            expires_at=int(time.time()) + 2592000,  # 30 days
        )
        self.tokens[mcp_token] = token

        # Store in token bridge
        self.token_bridge[mcp_token] = {
            "client_id": client.client_id,
            "scopes": token_scopes,
            "created_at": datetime.now(),
        }

        # Create and return OAuth token
        oauth_token = OAuthToken(
            access_token=mcp_token,
            token_type="Bearer",  # Fixed capitalization
            expires_in=2592000,  # 30 days
            scope=" ".join(token_scopes),
            refresh_token=refresh_token.token,  # Keep same refresh token
        )

        logger.info(colored(f"Refreshed token issued: {mcp_token[:10]}...", "green"))
        return oauth_token

    async def revoke_token(self, token) -> None:
        """Revoke an access or refresh token."""
        # Try to remove from access tokens
        if hasattr(token, "token"):
            token_str = token.token
        else:
            token_str = str(token)

        # Remove from access tokens
        if token_str in self.tokens:
            del self.tokens[token_str]
            logger.info(colored(f"Access token revoked: {token_str[:10]}...", "green"))

        # Remove from refresh tokens
        if token_str in self.refresh_tokens:
            del self.refresh_tokens[token_str]
            logger.info(colored(f"Refresh token revoked: {token_str[:10]}...", "green"))

        # Remove from token bridge
        if token_str in self.token_bridge:
            del self.token_bridge[token_str]

    async def validate_google_id_token(
        self, token_str: str, audience_client_id: str
    ) -> Optional[Dict[str, Any]]:
        """Validates Google ID token and returns claims if valid."""
        try:
            # Verify the ID token while checking the audience
            # The audience should be your Google OAuth client ID
            # Using a timeout for the request to Google's certs URL
            certs_request = google_auth_requests.Request()

            # Verify the token against Google's public keys.
            # The `audience` parameter is crucial for security.
            id_info = google_id_token_verifier.verify_oauth2_token(
                token_str,
                certs_request,
                audience_client_id,
                clock_skew_in_seconds=10,  # allow some clock skew
            )

            # id_info contains the decoded token claims
            # Example claims: iss, azp, aud, sub, email, email_verified, at_hash, exp, iat, name, picture, given_name, family_name, locale

            if id_info.get("iss") not in [
                "accounts.google.com",
                "https://accounts.google.com",
            ]:
                raise ValueError("Wrong issuer.")
            # ID token is valid. Get the user's Google Account ID from the Sub claim.
            user_email = id_info.get("email")

            logger.info(
                colored(
                    f"Google ID token validated successfully for email: {user_email}",
                    "green",
                )
            )
            return id_info
        except ValueError as e:
            # Invalid token
            logger.error(colored(f"Google ID token validation error: {str(e)}", "red"))
            return None
        except Exception as e:
            logger.error(
                colored(
                    f"Unexpected error during Google ID token validation: {str(e)}",
                    "red",
                )
            )
            return None


class GoogleOAuthServer(BaseSearchServer):
    """Google OAuth server implementation."""

    def __init__(self):
        """Initialize the Google OAuth server with OAuth provider."""
        super().__init__(server_name="google_oauth_server")
        logger.info(colored("Creating Google OAuth server", "green"))

        # Create OAuth provider
        self.oauth_provider = GoogleOAuthProvider()

        # Claude client will be registered through environment variable
        logger.info(
            colored(
                "Google OAuth provider created. Client details will be used from env vars.",
                "green",
            )
        )

    def authenticate_request(self, scope):
        """Authenticate requests using OAuth tokens."""
        # Override base implementation to check for token

        # Always accept any requests if DISABLE_ALL_AUTH is true
        if os.getenv("DISABLE_ALL_AUTH", "false").lower() == "true":
            logger.warning(
                colored(
                    f"GOOGLE-OAUTH SERVER: Bypassing token validation due to DISABLE_ALL_AUTH=true",
                    "yellow",
                )
            )
            return True

        # Check for authorization header
        auth_header = None
        for name, value in scope.get("headers", []):
            if name == b"authorization":
                auth_header = value.decode("utf-8")
                break

        if not auth_header or not auth_header.lower().startswith("bearer "):
            logger.error(
                colored(
                    "GOOGLE-OAUTH SERVER: Missing or invalid authorization header",
                    "red",
                )
            )
            return False

        token = auth_header[7:]  # Skip "Bearer " prefix
        logger.info(
            colored(
                f"GOOGLE-OAUTH SERVER: Received bearer token: {token[:10]}...", "cyan"
            )
        )

        # Check if token is valid in OAuth provider
        access_token = self.oauth_provider.tokens.get(token)
        if not access_token:
            logger.warning(
                colored(
                    f"GOOGLE-OAUTH SERVER: Token not found: {token[:10]}...", "yellow"
                )
            )
            return False

        # Check if expired
        if access_token.expires_at and access_token.expires_at < time.time():
            logger.warning(
                colored(
                    f"GOOGLE-OAUTH SERVER: Token expired: {token[:10]}...", "yellow"
                )
            )
            return False

        logger.info(colored(f"GOOGLE-OAUTH SERVER: Token validated", "green"))
        return True

    async def handle_custom_endpoint(
        self, path, method, scope, receive, send, cors_headers
    ):
        """Handle Google OAuth-specific endpoints."""
        # Handle token endpoint
        if path == "/token" and method == "POST":
            logger.info(colored("GOOGLE-OAUTH SERVER: Token endpoint called", "green"))

            # Extract request body for token exchange
            body = b""
            more_body = True
            while more_body:
                message = await receive()
                body += message.get("body", b"")
                more_body = message.get("more_body", False)

            # Parse request body
            try:
                if body:
                    body_str = body.decode("utf-8")
                    token_request = dict(urllib.parse.parse_qsl(body_str))
                else:
                    token_request = {}

                logger.debug(f"Token request: {token_request}")
            except Exception as e:
                logger.error(colored(f"Failed to parse token request: {str(e)}", "red"))
                await send(
                    {
                        "type": "http.response.start",
                        "status": 400,
                        "headers": cors_headers + [(b"content-type", b"text/plain")],
                    }
                )
                await send(
                    {
                        "type": "http.response.body",
                        "body": b"Invalid request format",
                    }
                )
                return True

            # Handle token request based on grant type
            grant_type = token_request.get("grant_type")

            if grant_type == "authorization_code":
                # Exchange authorization code for token
                code = token_request.get("code")
                redirect_uri = token_request.get("redirect_uri")
                client_id = token_request.get("client_id")
                if not client_id:
                    if CLAUDE_CLIENT_ID:
                        client_id = CLAUDE_CLIENT_ID
                    else:
                        logger.error(
                            "No client_id provided and CLAUDE_CLIENT_ID not set"
                        )
                        await send(
                            {
                                "type": "http.response.start",
                                "status": 400,
                                "headers": [(b"content-type", b"application/json")]
                                + list(cors_headers.items()),
                            }
                        )
                        await send(
                            {
                                "type": "http.response.body",
                                "body": json.dumps(
                                    {
                                        "error": "invalid_client",
                                        "error_description": "client_id is required",
                                    }
                                ).encode(),
                            }
                        )
                        return True
                code_verifier = token_request.get("code_verifier")

                logger.info(
                    colored(
                        f"Processing authorization_code token request with code: {code[:10] if code else 'None'}",
                        "green",
                    )
                )

                # Always generate a token for any valid-looking requests
                mcp_token = f"mcp_{secrets.token_hex(32)}"
                refresh_token = f"refresh_{secrets.token_hex(16)}"

                # Store token in OAuth provider
                token = AccessToken(
                    token=mcp_token,
                    client_id=client_id,
                    scopes=["claudeai", "read", "write", "profile", "admin"],
                    expires_at=int(time.time()) + 2592000,  # 30 days
                )
                self.oauth_provider.tokens[mcp_token] = token

                # Also store in token bridge for cross-component access
                self.oauth_provider.token_bridge[mcp_token] = {
                    "client_id": client_id,
                    "scopes": ["claudeai", "read", "write", "profile", "admin"],
                    "created_at": datetime.now(),
                }

                # Format token response
                response_body = json.dumps(
                    {
                        "access_token": mcp_token,
                        "token_type": "bearer",
                        "expires_in": 2592000,  # 30 days
                        "scope": "claudeai read write profile admin",
                        "refresh_token": refresh_token,
                    }
                ).encode("utf-8")

                # Add content-type to headers
                content_headers = cors_headers + [
                    (b"content-type", b"application/json")
                ]

                # Send response
                await send(
                    {
                        "type": "http.response.start",
                        "status": 200,
                        "headers": content_headers,
                    }
                )
                await send(
                    {
                        "type": "http.response.body",
                        "body": response_body,
                    }
                )
                return True

            elif grant_type == "refresh_token":
                # Handle refresh token
                refresh_token = token_request.get("refresh_token")
                client_id = token_request.get("client_id")
                if not client_id:
                    if CLAUDE_CLIENT_ID:
                        client_id = CLAUDE_CLIENT_ID
                    else:
                        logger.error(
                            "No client_id provided and CLAUDE_CLIENT_ID not set"
                        )
                        await send(
                            {
                                "type": "http.response.start",
                                "status": 400,
                                "headers": [(b"content-type", b"application/json")]
                                + list(cors_headers.items()),
                            }
                        )
                        await send(
                            {
                                "type": "http.response.body",
                                "body": json.dumps(
                                    {
                                        "error": "invalid_client",
                                        "error_description": "client_id is required",
                                    }
                                ).encode(),
                            }
                        )
                        return True

                # Always generate a new token
                mcp_token = f"mcp_{secrets.token_hex(32)}"

                # Store token in OAuth provider
                token = AccessToken(
                    token=mcp_token,
                    client_id=client_id,
                    scopes=["claudeai", "read", "write", "profile", "admin"],
                    expires_at=int(time.time()) + 2592000,  # 30 days
                )
                self.oauth_provider.tokens[mcp_token] = token

                # Also store in token bridge for cross-component access
                self.oauth_provider.token_bridge[mcp_token] = {
                    "client_id": client_id,
                    "scopes": ["claudeai", "read", "write", "profile", "admin"],
                    "created_at": datetime.now(),
                }

                # Format token response
                response_body = json.dumps(
                    {
                        "access_token": mcp_token,
                        "token_type": "bearer",
                        "expires_in": 2592000,  # 30 days
                        "scope": "claudeai read write profile admin",
                        "refresh_token": refresh_token,  # Keep the same refresh token
                    }
                ).encode("utf-8")

                # Add content-type to headers
                content_headers = cors_headers + [
                    (b"content-type", b"application/json")
                ]

                # Send response
                await send(
                    {
                        "type": "http.response.start",
                        "status": 200,
                        "headers": content_headers,
                    }
                )
                await send(
                    {
                        "type": "http.response.body",
                        "body": response_body,
                    }
                )
                return True

            else:
                # Unsupported grant type
                logger.error(colored(f"Unsupported grant type: {grant_type}", "red"))
                await send(
                    {
                        "type": "http.response.start",
                        "status": 400,
                        "headers": cors_headers + [(b"content-type", b"text/plain")],
                    }
                )
                await send(
                    {
                        "type": "http.response.body",
                        "body": b"Unsupported grant type",
                    }
                )
                return True

        # Handle Google OAuth callback
        elif path == "/oauth/callback":
            logger.info(
                colored("GOOGLE-OAUTH SERVER: OAuth callback endpoint called", "green")
            )

            # Extract query parameters
            query_params = {}
            if scope.get("query_string"):
                query_string = scope["query_string"].decode("utf-8")
                query_params = dict(urllib.parse.parse_qsl(query_string))

            # Log parameters for debugging
            logger.info(
                colored(
                    f"GOOGLE-OAUTH SERVER: Callback parameters: {query_params}", "cyan"
                )
            )

            # Handle Google OAuth callback
            state = query_params.get("state")
            code = query_params.get("code")
            error = query_params.get("error")

            # Check for errors
            if error:
                logger.error(colored(f"Google OAuth error: {error}", "red"))
                await send(
                    {
                        "type": "http.response.start",
                        "status": 400,
                        "headers": cors_headers + [(b"content-type", b"text/plain")],
                    }
                )
                await send(
                    {
                        "type": "http.response.body",
                        "body": f"Google OAuth error: {error}".encode("utf-8"),
                    }
                )
                return True

            # Check required parameters
            if not state or not code:
                logger.error(colored("Missing state or code parameter", "red"))
                await send(
                    {
                        "type": "http.response.start",
                        "status": 400,
                        "headers": cors_headers + [(b"content-type", b"text/plain")],
                    }
                )
                await send(
                    {
                        "type": "http.response.body",
                        "body": b"Missing state or code parameter",
                    }
                )
                return True

            # Load the original auth params from state
            original_auth_data = self.oauth_provider.auth_codes.get(state)
            if not original_auth_data:
                logger.error(colored(f"Invalid or expired state: {state}", "red"))
                await send(
                    {
                        "type": "http.response.start",
                        "status": 400,
                        "headers": cors_headers + [(b"content-type", b"text/plain")],
                    }
                )
                await send(
                    {
                        "type": "http.response.body",
                        "body": b"Invalid or expired state",
                    }
                )
                return True

            # Extract original client information
            original_client_id = original_auth_data.client_id
            original_client_redirect_uri = original_auth_data.redirect_uri
            original_scopes = original_auth_data.scopes

            # Safely get code challenge information with fallbacks
            original_code_challenge = getattr(original_auth_data, "code_challenge", "")

            # Exchange code for Google tokens
            google_client_id = os.getenv("GOOGLE_OAUTH_CLIENT_ID")
            google_client_secret = os.getenv("GOOGLE_OAUTH_CLIENT_SECRET")
            google_redirect_uri = os.getenv("GOOGLE_OAUTH_REDIRECT_URI")

            if (
                not google_client_id
                or not google_client_secret
                or not google_redirect_uri
            ):
                logger.error(
                    colored("Google OAuth environment variables not set", "red")
                )
                await send(
                    {
                        "type": "http.response.start",
                        "status": 500,
                        "headers": cors_headers + [(b"content-type", b"text/plain")],
                    }
                )
                await send(
                    {
                        "type": "http.response.body",
                        "body": b"Google OAuth environment variables not set",
                    }
                )
                return True

            # Exchange code for token
            token_req_data = {
                "code": code,
                "client_id": google_client_id,
                "client_secret": google_client_secret,
                "redirect_uri": google_redirect_uri,
                "grant_type": "authorization_code",
            }

            try:
                token_response = requests.post(GOOGLE_TOKEN_URL, data=token_req_data)
                token_response.raise_for_status()
                google_tokens = token_response.json()
                logger.info(colored(f"Google OAuth token exchange successful", "green"))
            except requests.exceptions.RequestException as e:
                logger.error(
                    colored(
                        f"Failed to exchange Google code for token: {str(e)}", "red"
                    )
                )
                await send(
                    {
                        "type": "http.response.start",
                        "status": 500,
                        "headers": cors_headers + [(b"content-type", b"text/plain")],
                    }
                )
                await send(
                    {
                        "type": "http.response.body",
                        "body": b"Failed to exchange Google code for token",
                    }
                )
                return True
            except Exception as e:
                logger.error(
                    colored(f"Google token exchange request failed: {str(e)}", "red")
                )
                await send(
                    {
                        "type": "http.response.start",
                        "status": 500,
                        "headers": cors_headers + [(b"content-type", b"text/plain")],
                    }
                )
                await send(
                    {
                        "type": "http.response.body",
                        "body": b"Google token exchange request failed",
                    }
                )
                return True

            google_access_token = google_tokens.get("access_token")
            google_refresh_token = google_tokens.get(
                "refresh_token"
            )  # May not always be present
            google_id_token = google_tokens.get("id_token")  # Contains user info

            logger.info(
                colored(
                    f"Google tokens received. Access token: {google_access_token[:10]}... ID token: {google_id_token[:10]}...",
                    "green",
                )
            )

            user_email = None
            if google_id_token and google_client_id:
                id_info = await self.oauth_provider.validate_google_id_token(
                    google_id_token, google_client_id
                )
                if id_info:
                    user_email = id_info.get("email")
                    logger.info(
                        colored(
                            f"Authenticated Google User Email: {user_email}", "magenta"
                        )
                    )

                    allowed_emails_str = os.getenv("ALLOWED_GOOGLE_EMAIL")
                    if allowed_emails_str and user_email:
                        # Split by comma and strip whitespace
                        allowed_emails = [
                            email.strip().lower()
                            for email in allowed_emails_str.split(",")
                        ]
                        if user_email.lower() not in allowed_emails:
                            logger.error(
                                colored(
                                    f"Google user {user_email} is not in the allowed users list ({allowed_emails_str}). Access denied.",
                                    "red",
                                )
                            )
                            await send(
                                {
                                    "type": "http.response.start",
                                    "status": 403,
                                    "headers": cors_headers
                                    + [(b"content-type", b"text/plain")],
                                }
                            )
                            await send(
                                {
                                    "type": "http.response.body",
                                    "body": b"Access denied. User not authorized.",
                                }
                            )
                            return True
                        else:
                            logger.info(
                                colored(
                                    f"Google user {user_email} is in the allowed users list. Access granted.",
                                    "green",
                                )
                            )
                    else:
                        logger.info(
                            colored(
                                f"No specific ALLOWED_GOOGLE_EMAIL set, user {user_email} access granted by default.",
                                "yellow",
                            )
                        )
                else:
                    logger.error(
                        colored(
                            "Failed to validate Google ID token or extract email.",
                            "red",
                        )
                    )
                    await send(
                        {
                            "type": "http.response.start",
                            "status": 401,
                            "headers": cors_headers
                            + [(b"content-type", b"text/plain")],
                        }
                    )
                    await send(
                        {
                            "type": "http.response.body",
                            "body": b"Failed to validate Google identity.",
                        }
                    )
                    return True
            else:
                logger.warning(
                    colored(
                        "Google ID token or Google Client ID not available for validation.",
                        "yellow",
                    )
                )
                # Depending on policy, might deny access if ID token is missing

            # Now, generate our server's (MCP) authorization code
            mcp_auth_code_str = f"mcp_code_{secrets.token_hex(16)}"
            mcp_auth_code = AuthorizationCode(
                code=mcp_auth_code_str,
                client_id=original_client_id,  # The client that made the original /authorize call
                redirect_uri=AnyHttpUrl(
                    str(original_client_redirect_uri)
                ),  # Their redirect URI
                redirect_uri_provided_explicitly=True,
                expires_at=time.time() + 600,  # 10 minutes for our code
                scopes=original_scopes,
                code_challenge=original_code_challenge,
            )

            # Store MCP code in OAuth provider
            self.oauth_provider.auth_codes[mcp_auth_code_str] = mcp_auth_code

            # Copy PKCE challenge method from original state to new MCP code
            if state in self.oauth_provider.pkce_challenge_methods:
                self.oauth_provider.pkce_challenge_methods[mcp_auth_code_str] = (
                    self.oauth_provider.pkce_challenge_methods[state]
                )
                logger.info(
                    colored(
                        f"Copied PKCE method for MCP code: {mcp_auth_code_str[:10]}...",
                        "cyan",
                    )
                )

            # Store Google-specific information in a separate dictionary
            # keyed by the MCP auth code
            google_info = {
                "google_access_token": google_access_token,
                "google_refresh_token": google_refresh_token,
                "google_id_token": google_id_token,
            }
            if user_email:
                google_info["google_user_email"] = user_email

            # Store this information in the OAuth provider's state
            if not hasattr(self.oauth_provider, "google_auth_info"):
                self.oauth_provider.google_auth_info = {}
            self.oauth_provider.google_auth_info[mcp_auth_code_str] = google_info

            # Redirect back to the original client (usually Claude) with our code
            redirect_url = construct_redirect_uri(
                str(original_client_redirect_uri), code=mcp_auth_code_str, state=state
            )

            logger.info(
                colored(
                    f"Redirecting to original client with MCP code: {redirect_url}",
                    "green",
                )
            )

            # Send redirect response
            await send(
                {
                    "type": "http.response.start",
                    "status": 302,
                    "headers": cors_headers
                    + [(b"location", redirect_url.encode("utf-8"))],
                }
            )
            await send(
                {
                    "type": "http.response.body",
                    "body": b"",
                }
            )
            return True

        # Handle authorize endpoint
        elif path == "/authorize":
            logger.info(
                colored("GOOGLE-OAUTH SERVER: Authorize endpoint called", "green")
            )

            # Extract query parameters
            query_params = {}
            if scope.get("query_string"):
                query_string = scope["query_string"].decode("utf-8")
                query_params = dict(urllib.parse.parse_qsl(query_string))

            # Log parameters for debugging
            logger.info(
                colored(
                    f"GOOGLE-OAUTH SERVER: Authorize parameters: {query_params}", "cyan"
                )
            )

            # Extract required parameters
            client_id = query_params.get("client_id", CLAUDE_CLIENT_ID)
            redirect_uri = query_params.get("redirect_uri")
            state = query_params.get("state")
            response_type = query_params.get("response_type")
            scope = query_params.get("scope", "")
            code_challenge = query_params.get("code_challenge")
            code_challenge_method = query_params.get(
                "code_challenge_method", "plain"
            )  # RFC 7636 default

            # Keep redirect_uri as-is (localhost will work for same-machine access)

            # Minimal validation
            if not redirect_uri:
                logger.error(colored("Missing redirect_uri parameter", "red"))
                await send(
                    {
                        "type": "http.response.start",
                        "status": 400,
                        "headers": cors_headers + [(b"content-type", b"text/plain")],
                    }
                )
                await send(
                    {
                        "type": "http.response.body",
                        "body": b"Missing redirect_uri parameter",
                    }
                )
                return True

            # PKCE validation according to RFC 7636
            if code_challenge:
                if code_challenge_method not in ["S256", "plain"]:
                    logger.error(
                        colored(
                            f"Unsupported code challenge method: {code_challenge_method}",
                            "red",
                        )
                    )
                    await send(
                        {
                            "type": "http.response.start",
                            "status": 400,
                            "headers": cors_headers
                            + [(b"content-type", b"text/plain")],
                        }
                    )
                    await send(
                        {
                            "type": "http.response.body",
                            "body": f"Unsupported code challenge method: {code_challenge_method}".encode(
                                "utf-8"
                            ),
                        }
                    )
                    return True

                logger.info(
                    colored(
                        f"PKCE enabled with method: {code_challenge_method}, challenge: {code_challenge[:10]}...",
                        "green",
                    )
                )

            # Create AuthorizationParams
            auth_params = AuthorizationParams(
                redirect_uri=AnyUrl(redirect_uri),
                redirect_uri_provided_explicitly=True,
                state=state,
                scopes=scope.split() if scope else [],
                code_challenge=code_challenge or "",
            )

            # Use our OAuth provider to get redirect URL to Google
            try:
                # Create a dummy client info for this request
                client_info = OAuthClientInformationFull(
                    client_id=client_id,
                    client_secret="dummy-secret",
                    redirect_uris=[AnyUrl(redirect_uri)],
                )

                # Store PKCE challenge method if provided
                if code_challenge and state:
                    self.oauth_provider.pkce_challenge_methods[state] = (
                        code_challenge_method
                    )
                    logger.info(
                        colored(
                            f"Stored PKCE method {code_challenge_method} for state {state[:10]}...",
                            "cyan",
                        )
                    )

                # Get redirect URL to Google's auth page
                redirect_url = await self.oauth_provider.authorize(
                    client_info, auth_params
                )

                # Send redirect response
                await send(
                    {
                        "type": "http.response.start",
                        "status": 302,
                        "headers": cors_headers
                        + [(b"location", redirect_url.encode("utf-8"))],
                    }
                )
                await send(
                    {
                        "type": "http.response.body",
                        "body": b"",
                    }
                )
                return True

            except Exception as e:
                logger.error(colored(f"Error in authorize endpoint: {str(e)}", "red"))
                await send(
                    {
                        "type": "http.response.start",
                        "status": 500,
                        "headers": cors_headers + [(b"content-type", b"text/plain")],
                    }
                )
                await send(
                    {
                        "type": "http.response.body",
                        "body": f"Authorization error: {str(e)}".encode("utf-8"),
                    }
                )
                return True

        # Handle client registration endpoint for dynamic client registration (RFC 7591)
        elif path == "/register" and method == "POST":
            logger.info(
                colored(
                    "GOOGLE-OAUTH SERVER: Client registration endpoint called", "green"
                )
            )

            # Extract request body for client registration
            body = b""
            more_body = True
            while more_body:
                message = await receive()
                body += message.get("body", b"")
                more_body = message.get("more_body", False)

            # Parse registration request
            try:
                if body:
                    body_str = body.decode("utf-8")
                    registration_request = json.loads(body_str)
                else:
                    registration_request = {}

                logger.debug(f"Client registration request: {registration_request}")
            except Exception as e:
                logger.error(
                    colored(
                        f"Failed to parse client registration request: {str(e)}", "red"
                    )
                )
                await send(
                    {
                        "type": "http.response.start",
                        "status": 400,
                        "headers": cors_headers
                        + [(b"content-type", b"application/json")],
                    }
                )
                await send(
                    {
                        "type": "http.response.body",
                        "body": json.dumps(
                            {
                                "error": "invalid_request",
                                "error_description": "Invalid request format",
                            }
                        ).encode("utf-8"),
                    }
                )
                return True

            # Generate client credentials
            client_id = f"client_{secrets.token_hex(16)}"
            client_secret = f"secret_{secrets.token_hex(32)}"

            # Extract client metadata
            client_name = registration_request.get("client_name", "MCP Client")
            redirect_uris = registration_request.get("redirect_uris", [])
            grant_types = registration_request.get(
                "grant_types", ["authorization_code", "refresh_token"]
            )
            response_types = registration_request.get("response_types", ["code"])
            scope = registration_request.get(
                "scope", "claudeai read write profile admin"
            )
            scopes = scope.split() if isinstance(scope, str) else scope

            # Create client information
            client_info = OAuthClientInformationFull(
                client_id=client_id,
                client_secret=client_secret,
                redirect_uris=(
                    [AnyUrl(uri) for uri in redirect_uris]
                    if redirect_uris
                    else [AnyUrl("http://localhost:8080/callback")]
                ),
            )

            # Register client with OAuth provider
            await self.oauth_provider.register_client(client_info)

            # Prepare registration response according to RFC 7591
            registration_response = {
                "client_id": client_id,
                "client_secret": client_secret,
                "client_name": client_name,
                "redirect_uris": redirect_uris,
                "grant_types": grant_types,
                "response_types": response_types,
                "scope": " ".join(scopes),
                "token_endpoint_auth_method": "client_secret_post",
                "client_id_issued_at": int(time.time()),
                "client_secret_expires_at": 0,  # Never expires
            }

            logger.info(
                colored(f"Client registered successfully: {client_id}", "green")
            )

            # Send registration response
            await send(
                {
                    "type": "http.response.start",
                    "status": 201,
                    "headers": cors_headers + [(b"content-type", b"application/json")],
                }
            )
            await send(
                {
                    "type": "http.response.body",
                    "body": json.dumps(registration_response).encode("utf-8"),
                }
            )
            return True

        # Handle OAuth 2.0 discovery endpoint (for compatibility)
        elif path == "/.well-known/oauth-authorization-server":
            logger.info(
                colored("GOOGLE-OAUTH SERVER: OAuth discovery endpoint called", "green")
            )

            # Get server URL from environment or default to localhost
            # Use the SERVER_PORT environment variable or the default port
            port = os.getenv("SERVER_PORT", "8511")
            server_url = os.getenv("SERVER_URL", f"http://localhost:{port}")

            # Format discovery document with client registration support
            discovery_data = {
                "issuer": server_url,
                "authorization_endpoint": f"{server_url}/authorize",
                "token_endpoint": f"{server_url}/token",
                "registration_endpoint": f"{server_url}/register",  # Added for dynamic client registration
                "token_endpoint_auth_methods_supported": [
                    "client_secret_basic",
                    "client_secret_post",
                    "none",
                ],
                "scopes_supported": ["claudeai", "read", "write", "profile", "admin"],
                "response_types_supported": ["code"],
                "grant_types_supported": ["authorization_code", "refresh_token"],
                "revocation_endpoint": f"{server_url}/revoke",
                "service_documentation": f"{server_url}/docs",
                # PKCE support according to RFC 7636
                "code_challenge_methods_supported": [
                    "S256",
                    "plain",
                ],  # S256 is mandatory for PKCE per RFC 7636
                # Additional fields for dynamic client registration
                "client_registration_endpoint": f"{server_url}/register",
                "registration_endpoint_auth_methods_supported": [
                    "none"
                ],  # No auth required for registration
                "dynamic_client_registration_supported": True,
            }

            await send(
                {
                    "type": "http.response.start",
                    "status": 200,
                    "headers": cors_headers + [(b"content-type", b"application/json")],
                }
            )
            await send(
                {
                    "type": "http.response.body",
                    "body": json.dumps(discovery_data).encode(),
                }
            )
            return True

        # If we got here, the endpoint was not handled
        return False

    async def get_health_data(self):
        """Get server health data for healthcheck endpoint."""
        base_data = await super().get_health_data()
        base_data.update(
            {
                "server": "google_oauth_server",
                "time": str(datetime.now()),
                "oauth_enabled": True,
                "token_count": len(self.oauth_provider.tokens),
                "auth_code_count": len(self.oauth_provider.auth_codes),
                "google_oauth_client_id": os.getenv(
                    "GOOGLE_OAUTH_CLIENT_ID", "not_set"
                ),
                "google_oauth_redirect_uri": os.getenv(
                    "GOOGLE_OAUTH_REDIRECT_URI", "not_set"
                ),
                "allowed_google_email": os.getenv("ALLOWED_GOOGLE_EMAIL", "not_set"),
            }
        )
        return base_data


def create_google_oauth_server():
    """Create a Google OAuth server."""
    return GoogleOAuthServer()


def run_google_oauth_server(port=8511):
    """Run a Google OAuth server."""
    server = create_google_oauth_server()
    server.run_server(port)
