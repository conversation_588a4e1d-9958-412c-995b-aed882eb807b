import os
from dotenv_vault import load_dotenv
load_dotenv()
try:
    # Get all environment variables
    env_vars = os.environ
    
    # Filter and print variables containing '_API_'
    api_vars = {key: value for key, value in env_vars.items() if '_API_' in key}
    
    if api_vars:
        print("Environment variables containing '_API_':")
        for key, value in api_vars.items():
            print(f"{key}: {value}")
    else:
        print("No environment variables containing '_API_' were found.")
except Exception as e:
    print(f"Error while processing environment variables: {e}")
