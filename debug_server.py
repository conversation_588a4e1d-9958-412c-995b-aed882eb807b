#!/usr/bin/env python
import os
import sys
from termcolor import colored

# Add the project root to path so Python can find the modules
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

try:
    # Now import the server module using absolute imports
    from src.search_server import server
    
    # Run the server
    print(colored("Starting search server in debug mode...", "cyan"))
    server.main()
except ImportError as e:
    print(colored(f"Import error: {e}", "red"))
    print(colored("Make sure all dependencies are installed:", "yellow"))
    print(colored("pip install -r requirements.txt", "yellow"))
    sys.exit(1)
except Exception as e:
    print(colored(f"Error: {e}", "red"))
    import traceback
    traceback.print_exc()
    sys.exit(1) 