# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build/Lint/Test Commands

- Install dependencies: `uv sync`
- Build package: `uv build`
- Run server locally: `uv run search-server`
- Run all tests: `python run_tests.py`
- Run single test: `pytest -xvs tests/test_name.py`
- Run specific test function: `pytest -xvs tests/test_name.py::test_function_name`
- Type checking: `pyright src`

## Code Style Guidelines

- **Imports**: Group imports by standard library, third-party, and local modules with a blank line between groups
- **Type Hints**: Use typing module annotations for all function parameters and return values
- **Error Handling**: Use try/except blocks with specific exception types and proper logging
- **Async**: Use asyncio and aiohttp for asynchronous operations
- **Environment Variables**: Load with dotenv_vault and validate required variables at startup
- **Logging**: Use the central logger module for consistent logging
- **Testing**: Write pytest tests with descriptive names prefixed with `test_`
- **Documentation**: Include docstrings for modules, classes, and functions
- **Cursor Rule**: Always leave server restart to the user

## Project Structure

The codebase follows a modular approach with the main functionality organized in the `/src/search_server` directory:

```
src/search_server/
├── __init__.py                 # Package exports and entry point
├── server.py                   # Main server selection logic
├── server_config.py            # Environment and server configuration
├── logger.py                   # Centralized logging configuration
├── token_bridge.py             # Shared token storage for authentication
├── direct_server.py            # Direct server without OAuth
├── direct_oauth_server.py      # Direct OAuth server with simplified auth
├── google_oauth_server.py      # Google OAuth server with email validation
├── search_engine.py            # Search engine implementation
├── web_scraper.py              # Web scraping functionality
├── knowledge_base.py           # Knowledge base search
├── markdown_to_pdf.py          # Markdown to PDF conversion
├── pandoc_markdown_to_pdf.py   # Pandoc-based PDF conversion
├── finnhub_api.py              # Financial data API client
├── groundx_api.py              # GroundX API client
└── utils/                      # Shared utility modules
    ├── __init__.py             # Utils package exports
    ├── logging_utils.py        # Logging utilities
    ├── tools.py                # Common tool definitions
    ├── handlers.py             # Common handler functions
    └── base_server.py          # Base server class
```

Also available:
- `run_server.sh` - Script to run the server
- `run_refactored_server.py` - Script to run refactored server implementations

## Server Implementations

The server offers multiple implementation options:

1. **Direct Server**: Simple implementation without OAuth authentication (direct_server.py)
   - Used when `DISABLE_ALL_AUTH=true`
   - Provides the simplest path for testing with Claude

2. **Direct OAuth Server**: Server with simplified OAuth flow (direct_oauth_server.py)
   - Combines direct server pattern with OAuth
   - Bypasses typical OAuth flow by auto-generating tokens
   - Provides special handling for Claude requests

3. **Google OAuth Server**: Server with Google OAuth integration (google_oauth_server.py)
   - Full Google OAuth integration with token validation
   - Supports user email validation via `ALLOWED_GOOGLE_EMAIL` environment variable
   - Supports multiple users by using comma-separated emails in `ALLOWED_GOOGLE_EMAIL`
