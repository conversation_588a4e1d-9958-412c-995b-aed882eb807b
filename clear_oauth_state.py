"""
<PERSON><PERSON><PERSON> to clear OAuth state and authentication tokens to force a fresh authentication flow.
This is useful for testing the full OAuth PKCE process with Google.
"""

import os
import sys
import json
import argparse
import logging
from pathlib import Path
from datetime import datetime
import webbrowser
import requests
from termcolor import colored

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("oauth-state-clearer")

def find_config_path():
    """Find the Claude Desktop config path based on OS."""
    if sys.platform.startswith("darwin"):  # macOS
        return os.path.expanduser("~/Library/Application Support/Claude/claude_desktop_config.json")
    elif sys.platform.startswith("win"):  # Windows
        return os.path.join(os.environ.get("APPDATA", ""), "<PERSON>", "claude_desktop_config.json")
    else:  # Linux
        return os.path.expanduser("~/.config/<PERSON>/claude_desktop_config.json")

def clear_server_oauth_state():
    """Clear OAuth state in the server by removing authentication tokens."""
    # Try to use the debug API endpoint first
    server_port = os.environ.get("SEARCH_SERVER_PORT", "8511")
    server_url = f"http://localhost:{server_port}"
    
    logger.info(colored(f"Attempting to clear OAuth state via API at {server_url}/debug/clear_oauth_state", "cyan"))
    
    try:
        response = requests.get(f"{server_url}/debug/clear_oauth_state", timeout=5)
        if response.status_code == 200:
            logger.info(colored("Server OAuth state cleared successfully via API", "green"))
            return True
        else:
            logger.warning(colored(f"API returned non-200 status: {response.status_code}", "yellow"))
            logger.warning(colored(f"Response: {response.text}", "yellow"))
    except Exception as e:
        logger.error(colored(f"Failed to clear OAuth state via API: {e}", "red"))
        logger.info("Falling back to local token file method")
    
    # Fallback: Clear local token file
    try:
        # Create a backup of authenticated_tokens.json if it exists
        tokens_path = Path("./authenticated_tokens.json")
        if tokens_path.exists():
            backup_path = tokens_path.with_suffix(f".backup.{datetime.now().strftime('%Y%m%d%H%M%S')}")
            tokens_path.rename(backup_path)
            logger.info(colored(f"Backed up tokens to {backup_path}", "cyan"))
        
        # Create empty authenticated_tokens.json
        with open(tokens_path, "w") as f:
            json.dump({}, f)
        logger.info(colored("Created empty tokens file", "green"))
        return True
    except Exception as e:
        logger.error(colored(f"Error clearing server OAuth state: {e}", "red"))
        return False

def revoke_google_token():
    """Open browser to Google's permissions page to revoke OAuth tokens."""
    permissions_url = "https://myaccount.google.com/permissions"
    security_url = "https://myaccount.google.com/security"
    
    logger.info(colored("\nOpening Google account permissions page...", "cyan"))
    logger.info(colored("Please follow these steps:", "cyan"))
    logger.info("1. Look for ANY third-party apps with 'Search', 'Claude', 'Test', or similar in the name")
    logger.info("   (The app might be listed as 'Google API' or the OAuth Client ID name)")
    logger.info("2. Click on each one and select 'Remove Access'")
    logger.info("3. Confirm the revocation when prompted")
    
    try:
        webbrowser.open(permissions_url)
        input(colored("\nPress Enter when you have checked for and revoked any relevant apps...", "yellow"))
        
        # Also check security page for connected apps
        logger.info(colored("\nNow opening Google security page to check for additional connected apps...", "cyan"))
        logger.info("1. Scroll down to 'Third-party apps with account access'")
        logger.info("2. Check for any remaining connected apps")
        
        webbrowser.open(security_url)
        input(colored("\nPress Enter when you have checked the security page...", "yellow"))
    except Exception as e:
        logger.error(colored(f"Failed to open browser: {e}", "red"))
        logger.info(colored(f"Please manually visit: {permissions_url} and {security_url}", "yellow"))
        input(colored("\nPress Enter when you have revoked access in the browser...", "yellow"))

def clear_browser_cookies():
    """Show instructions for clearing browser cookies for Google."""
    logger.info(colored("\nTo clear browser cookies related to Google OAuth:", "cyan"))
    
    logger.info(colored("\nChrome/Chromium:", "cyan"))
    logger.info("1. Press Ctrl+Shift+Delete (or ⌘+Shift+Delete on Mac)")
    logger.info("2. Select 'Cookies and other site data'")
    logger.info("3. Set time range to 'All time'")
    logger.info("4. Add ALL of these domains to the sites list:")
    logger.info("   - accounts.google.com")
    logger.info("   - google.com")
    logger.info("   - localhost")
    logger.info("   - omni.ngrok.pro")
    logger.info("   - claude.ai")
    logger.info("5. Click 'Clear data'")
    
    logger.info(colored("\nFirefox:", "cyan"))
    logger.info("1. Press Ctrl+Shift+Delete (or ⌘+Shift+Delete on Mac)")
    logger.info("2. Select 'Cookies'")
    logger.info("3. Set time range to 'Everything'")
    logger.info("4. Click the 'Clear Now' button")
    
    logger.info(colored("\nSafari:", "cyan"))
    logger.info("1. Go to Safari > Preferences > Privacy")
    logger.info("2. Click 'Manage Website Data'")
    logger.info("3. Search for 'Google', 'Claude', and 'localhost'")
    logger.info("4. Select and remove all those entries")
    
    logger.info(colored("\nEASIEST OPTION: Use a private/incognito window for testing", "green"))
    logger.info("This automatically prevents cookies from previous sessions affecting your test")
    
    response = input(colored("\nWill you use incognito/private window for testing? (y/n): ", "yellow")).lower()
    if response != 'y':
        input(colored("Press Enter when you have cleared your browser cookies...", "yellow"))
    else:
        logger.info(colored("Great! When testing with Claude Desktop, please open your browser in incognito/private mode.", "green"))

def check_server_status():
    """Check if the search server is running and accessible."""
    server_port = os.environ.get("SEARCH_SERVER_PORT", "8511")
    server_url = f"http://localhost:{server_port}"
    
    try:
        response = requests.get(f"{server_url}/auth/debug", timeout=2)
        if response.status_code == 200:
            logger.info(colored("✓ Search server is running and accessible", "green"))
            return True
        else:
            logger.warning(colored(f"✗ Search server returned unexpected status: {response.status_code}", "yellow"))
    except requests.RequestException:
        logger.error(colored("✗ Search server is not running or not accessible", "red"))
        logger.info(colored("Please start the server with: uv run search-server", "yellow"))
    
    return False

def main():
    parser = argparse.ArgumentParser(description="Clear OAuth state to force a fresh authentication flow")
    parser.add_argument("--all", action="store_true", help="Clear all state and show all instructions")
    parser.add_argument("--server", action="store_true", help="Clear server-side OAuth state")
    parser.add_argument("--google", action="store_true", help="Revoke Google permissions")
    parser.add_argument("--cookies", action="store_true", help="Show instructions for clearing cookies")
    parser.add_argument("--port", type=str, help="Server port (default: 8511 or SEARCH_SERVER_PORT env var)")
    
    args = parser.parse_args()
    
    # Default to all if no specific option is selected
    if not (args.all or args.server or args.google or args.cookies):
        args.all = True
    
    # Set port from argument if provided
    if args.port:
        os.environ["SEARCH_SERVER_PORT"] = args.port
    
    logger.info(colored("=== OAuth State Cleanup Utility ===", "cyan"))
    
    if args.all or args.server:
        server_running = check_server_status()
        if server_running:
            clear_server_oauth_state()
        else:
            logger.warning(colored("Server-side OAuth state could not be cleared. Please start the server and try again.", "yellow"))
            if input(colored("Would you like to continue with client-side cleanup? (y/n): ", "yellow")).lower() != 'y':
                return
    
    if args.all or args.google:
        revoke_google_token()
    
    if args.all or args.cookies:
        clear_browser_cookies()
    
    logger.info(colored("\n=== OAuth State Cleanup Complete ===", "green"))
    logger.info(colored("You can now test the full PKCE authentication flow with Claude Desktop.", "green"))
    logger.info(colored("Command to start search server: uv run search-server", "yellow"))

if __name__ == "__main__":
    main()