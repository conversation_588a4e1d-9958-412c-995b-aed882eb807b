# search_server MCP server

Search the web using multiple search engines (<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)

## Components

### Resources

The server implements a simple note storage system with:
- Custom note:// URI scheme for accessing individual notes
- Each note resource has a name, description and text/plain mimetype

### Prompts

The server provides a single prompt:
- summarize-notes: Creates summaries of all stored notes
  - Optional "style" argument to control detail level (brief/detailed)
  - Generates prompt combining all current notes with style preference

### Tools

The server implements one tool:
- add-note: Adds a new note to the server
  - Takes "name" and "content" as required string arguments
  - Updates server state and notifies clients of resource changes

## Configuration

[TODO: Add configuration details specific to your implementation]

## Quickstart

### Install

#### Claude Desktop

On MacOS: `~/Library/Application\ Support/Claude/claude_desktop_config.json`
On Windows: `%APPDATA%/Claude/claude_desktop_config.json`

<details>
  <summary>Development/Unpublished Servers Configuration</summary>
  ```
  "mcpServers": {
    "search_server": {
      "command": "uv",
      "args": [
        "--directory",
        "C:\Work\Research\AI\search_server",
        "run",
        "search_server"
      ]
    }
  }
  ```
</details>

<details>
  <summary>Published Servers Configuration</summary>
  ```
  "mcpServers": {
    "search_server": {
      "command": "uvx",
      "args": [
        "search_server"
      ]
    }
  }
  ```
</details>

## Development

### Building and Publishing

To prepare the package for distribution:

1. Sync dependencies and update lockfile:
```bash
uv sync
```

2. Build package distributions:
```bash
uv build
```

This will create source and wheel distributions in the `dist/` directory.

3. Publish to PyPI:
```bash
uv publish
```

Note: You'll need to set PyPI credentials via environment variables or command flags:
- Token: `--token` or `UV_PUBLISH_TOKEN`
- Or username/password: `--username`/`UV_PUBLISH_USERNAME` and `--password`/`UV_PUBLISH_PASSWORD`

### Server Modes

This project supports three server operation modes to accommodate different use cases.

```bash
# Run the server with default settings (Direct OAuth Server with Google OAuth)
uv run search-server

# Run on a specific port
uv run search-server --port 8511

# Or use the run_server.sh script for an interactive menu
./run_server.sh
```

#### Available Server Implementations

1. **Direct OAuth Server with Google OAuth (DEFAULT & RECOMMENDED)** - Combines direct server pattern with full Google OAuth:
   ```bash
   # This is the default mode - no special configuration needed
   uv run search-server
   
   # Or explicitly enable it
   export GOOGLE_OAUTH_ENABLE="true"
   uv run search-server
   ```
   
   This implementation provides full Google OAuth security while ensuring tools are visible and working in Claude.

2. **Google OAuth Stream Server** - Enhanced streaming implementation with Google OAuth:
   ```bash
   # Run via environment variable
   export GOOGLE_OAUTH_STREAM_ENABLE="true"
   uv run search-server
   ```
   
   This implementation leverages server-sent events (SSE) for real-time streaming with Google OAuth authentication.

3. **Direct OAuth Server without Google OAuth** - OAuth-compatible but without Google authentication:
   ```bash
   # Run via environment variable
   export GOOGLE_OAUTH_ENABLE="false"
   uv run search-server
   ```
   
   This mode maintains the OAuth structure but doesn't require Google credentials, useful for simplified deployment.

4. **Direct Server** - No authentication (for testing only):
   ```bash
   # Run via environment variable
   export DISABLE_ALL_AUTH="true"
   uv run search-server
   ```

#### Server Comparison

| Feature                      | OAuth with Google | OAuth Stream | OAuth without Google | Direct Server |
|------------------------------|------------------|--------------|---------------------|---------------|
| Authentication               | Google OAuth     | Google OAuth | OAuth Structure     | None          |
| Tools visible in Claude      | ✅              | ✅           | ✅                 | ✅           |
| Security level               | High             | High         | Medium              | Low           |
| Compatible with Claude       | Full             | Full         | Full                | Full          |
| Requires Google credentials  | ✅              | ✅           | ❌                 | ❌           |
| Special Claude handling      | ✅              | ✅           | ✅                 | ✅           |
| Real-time streaming          | ❌              | ✅           | ❌                 | ❌           |
| Recommended for production   | ✅              | ✅           | ⚠️                | ❌           |

For a detailed explanation of the server architecture and implementation details, see [SERVER_ARCHITECTURE.md](SERVER_ARCHITECTURE.md).

### OAuth Authentication

This server supports Google OAuth authentication for secure access to MCP functionality. The OAuth implementation includes:

1. **Dynamic Client Registration (RFC 7591)**: Automatically registers MCP clients like Claude
2. **PKCE S256 Support (RFC 7636)**: Enhanced security with Proof Key for Code Exchange
3. **Google OAuth 2.0 Integration**: Authenticates users through Google's OAuth service
4. **Enhanced Logging**: Comprehensive error handling and debugging capabilities

#### MCP Client Compatibility

The server is fully compatible with:
- ✅ **Claude Desktop**: Complete OAuth flow with dynamic registration
- ✅ **Claude Code**: PKCE S256 support for enhanced security
- ✅ **MCP Inspector**: For debugging and development
- ✅ **Custom MCP Clients**: Dynamic client registration for any compliant client

#### Configuration

To set up OAuth authentication:

1. Create a Google OAuth 2.0 client in [Google Cloud Console](https://console.cloud.google.com/)
2. Add the following environment variables to your `.env` file:

```
GOOGLE_OAUTH_CLIENT_ID=your-google-client-id
GOOGLE_OAUTH_CLIENT_SECRET=your-google-client-secret
GOOGLE_OAUTH_REDIRECT_URI=https://your-domain.com/oauth/callback
GOOGLE_OAUTH_ENABLE=true
ALLOWED_GOOGLE_EMAIL=<EMAIL>  # Optional: restrict access
```

3. Ensure the redirect URI in Google Cloud Console matches the one in your .env file
4. To disable Google OAuth while maintaining the OAuth structure, set `GOOGLE_OAUTH_ENABLE=false`

#### OAuth Discovery and Registration

The server provides RFC-compliant OAuth discovery:
- **Discovery Endpoint**: `/.well-known/oauth-authorization-server`
- **Dynamic Registration**: `/register` (automatic client registration)
- **PKCE Support**: S256 and plain methods supported
- **Multiple Auth Methods**: client_secret_post, client_secret_basic, none

For detailed compatibility information with Claude clients, see [CLAUDE_COMPATIBILITY.md](CLAUDE_COMPATIBILITY.md).

#### Testing OAuth

The server includes debugging endpoints to help test OAuth authentication:

- `/auth` - Authentication page with login button
- `/auth/debug` - Shows OAuth status information
- `/debug-sse` - Test SSE connection without authentication

#### Client Scripts

Two client scripts are provided to demonstrate OAuth authentication:

1. `client_test_oauth.py` - Basic API authentication test
2. `sse_client_oauth.py` - Server-sent events with authentication

To run these scripts:

```bash
# Basic API authentication test
python client_test_oauth.py

# SSE client with authentication
python sse_client_oauth.py
```

These clients will:
1. Open a browser for authentication
2. Store tokens for reuse
3. Make authenticated API calls

#### For Production Use

When using this in production:

1. Implement proper token storage (database)
2. Add token refresh mechanisms
3. Consider using a more robust OAuth client registration system
4. Implement user management and permissions

### Logging

The server uses a comprehensive logging system with both centralized and component-specific logging. All logs are written to the `logs/` directory at the project root.

#### Main Features

- Centralized application logging with `logger.py`
- Component-specific logging with rotation support
- Console output with color coding for different log levels
- File output for persistent storage and debugging

#### Using the Logging System

To use the component-specific logging in your modules:

```python
from utils.logging_utils import setup_component_logger

# Set up component-specific logger (creates logs/your_component.log)
logger = setup_component_logger("your_component", enable_rotation=True)

# Use the logger
logger.info("Informational message")
logger.warning("Warning message")
logger.error("Error message")
logger.debug("Debug message - only shows if DEBUG level is enabled")
```

For components with date-sensitive information, you can use daily log rotation:

```python
from utils.logging_utils import setup_daily_rotating_logger

# Set up daily rotating logger (creates logs/your_component.log with daily rotation)
logger = setup_daily_rotating_logger("your_component")
```

#### Monitoring Logs

The server includes a monitoring script that supports viewing all log files:

```bash
# View main application log
python monitor_logs.py

# View component-specific log
python monitor_logs.py --component search_engine

# List all available log files
python monitor_logs.py --list

# Select log file interactively
python monitor_logs.py -i

# Show more initial lines
python monitor_logs.py --lines 50
```

For more details on the logging system, see [logs/README.md](logs/README.md).

### Debugging

Since MCP servers run over stdio, debugging can be challenging. For the best debugging
experience, we offer multiple debugging options:

#### Using MCP Inspector

The [MCP Inspector](https://github.com/modelcontextprotocol/inspector) provides a visual interface for debugging MCP servers.

You can launch the MCP Inspector via [`npm`](https://docs.npmjs.com/downloading-and-installing-node-js-and-npm) with this command:

```bash
npx @modelcontextprotocol/inspector uv --directory C:\Work\Research\AI\search_server run search-server
```

Upon launching, the Inspector will display a URL that you can access in your browser to begin debugging.

#### Using debug_server.py

For IDE-integrated debugging, use `debug_server.py` which enables you to:
- Set breakpoints in your code
- Debug directly from your cursor position in VS Code, PyCharm, or other IDEs
- Inspect variables during execution
- Step through code execution

This method is especially useful when debugging complex issues requiring detailed inspection of server behavior.

To use debug_server.py:
```bash
# Start in debug mode
python debug_server.py

# Or use your IDE's debug configuration to run debug_server.py
```

#### Some Alias in .zshrc

```bash
alias myuv="source .venv/bin/activate"
alias getenv="npx dotenv-vault@latest pull"
```
