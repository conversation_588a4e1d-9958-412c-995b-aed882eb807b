# Claude MCP Client Compatibility Guide

This guide explains how the search_server OAuth implementation works with different Claude MCP clients.

## Overview

The search_server implements modern OAuth standards to ensure full compatibility with <PERSON>'s MCP client ecosystem. This includes support for:

- **Dynamic Client Registration (RFC 7591)**: Automatic client setup
- **PKCE S256 Support (RFC 7636)**: Enhanced security for <PERSON> Code
- **OAuth Discovery (RFC 8414)**: Endpoint discovery and capability advertisement

## Claude Desktop Compatibility

### What Works
- ✅ OAuth discovery via `/.well-known/oauth-authorization-server`
- ✅ Automatic client registration via `/register` endpoint
- ✅ Standard OAuth authorization flow
- ✅ Token exchange and refresh
- ✅ All MCP tools and functionality

### Configuration
```json
{
  "mcpServers": {
    "search_server": {
      "command": "uv",
      "args": ["--directory", "/path/to/search_server", "run", "search-server"],
      "transport": {
        "type": "sse",
        "url": "https://your-domain.com/sse"
      }
    }
  }
}
```

### Authentication Flow
1. <PERSON> discovers OAuth endpoints
2. Automatically registers as OAuth client
3. Redirects user to Google OAuth
4. Receives tokens and starts MCP session

## Claude Code Compatibility

### What Works
- ✅ OAuth discovery and client registration
- ✅ PKCE S256 support for enhanced security
- ✅ Cross-machine browser authentication
- ✅ All MCP tools and functionality

### Enhanced Security
Claude Code uses PKCE (Proof Key for Code Exchange) with S256 method:
- Generates a code verifier and challenge
- Uses SHA256 hashing for security
- Prevents authorization code interception attacks

### Authentication Flow
1. Claude Code discovers OAuth capabilities including PKCE support
2. Registers as OAuth client with PKCE requirements
3. Generates code challenge using S256 method
4. User authenticates via browser (can be on different machine)
5. PKCE verification ensures secure token exchange

## MCP Inspector Compatibility

### What Works
- ✅ Full OAuth flow debugging
- ✅ Dynamic client registration testing
- ✅ PKCE flow testing
- ✅ Token management inspection

### Usage
```bash
npx @modelcontextprotocol/inspector uv --directory /path/to/search_server run search-server
```

## Custom MCP Client Integration

### Prerequisites
Your client must support:
- OAuth 2.0 authorization code flow
- Dynamic client registration (RFC 7591)
- OAuth discovery (RFC 8414)
- PKCE (RFC 7636) - recommended for security

### Integration Steps

1. **Discover OAuth Endpoints**
```http
GET /.well-known/oauth-authorization-server
```

2. **Register Your Client**
```http
POST /register
Content-Type: application/json

{
  "client_name": "My MCP Client",
  "redirect_uris": ["http://localhost:8080/callback"],
  "grant_types": ["authorization_code", "refresh_token"],
  "response_types": ["code"]
}
```

3. **Initiate OAuth Flow**
```http
GET /authorize?response_type=code&client_id={client_id}&code_challenge={challenge}&code_challenge_method=S256&redirect_uri={redirect_uri}&scope=claudeai
```

4. **Exchange Code for Token**
```http
POST /token
Content-Type: application/x-www-form-urlencoded

grant_type=authorization_code&code={auth_code}&code_verifier={verifier}&client_id={client_id}&redirect_uri={redirect_uri}
```

5. **Use Token for MCP Requests**
```http
GET /sse
Authorization: Bearer {access_token}
```

## Troubleshooting

### Common Issues

**"Incompatible auth server: does not support dynamic client registration"**
- ✅ **Fixed**: Server now implements RFC 7591 dynamic client registration

**"Incompatible auth server: does not support code challenge method S256"**
- ✅ **Fixed**: Server now advertises PKCE S256 support in discovery document

**"Client registration failed"**
- Check server logs for detailed error messages
- Verify request format matches RFC 7591 specification
- Ensure required fields (client_name, redirect_uris) are provided

**"PKCE verification failed"**
- Verify code_verifier matches the original challenge
- Ensure code_challenge_method is correctly specified (S256 or plain)
- Check for URL encoding issues in parameters

### Debug Endpoints

Use these endpoints for troubleshooting:

- `GET /auth/debug` - View OAuth status and configuration
- `GET /.well-known/oauth-authorization-server` - Verify discovery document
- Server logs - Check for detailed error messages with enhanced logging

### Logging

The server provides comprehensive logging for OAuth operations:
- Client registration details
- PKCE verification steps
- Token exchange operations
- Authentication failures with context

## Security Considerations

### Production Deployment
- Use HTTPS for all OAuth endpoints
- Implement proper token storage and rotation
- Configure `ALLOWED_GOOGLE_EMAIL` to restrict access
- Monitor OAuth logs for suspicious activity
- Regularly update OAuth client credentials

### Development Testing
- Use localhost redirect URIs for local development
- Set `DISABLE_AUTH_FOR_TESTING=true` only in development
- Test with multiple client types (Claude Desktop, Claude Code, custom)
- Verify PKCE flows work correctly

## Environment Variables Summary

```bash
# Required for OAuth
GOOGLE_OAUTH_CLIENT_ID=your-google-client-id
GOOGLE_OAUTH_CLIENT_SECRET=your-google-client-secret
GOOGLE_OAUTH_REDIRECT_URI=https://your-domain.com/oauth/callback

# Optional configuration
ALLOWED_GOOGLE_EMAIL=<EMAIL>,<EMAIL>
GOOGLE_OAUTH_ENABLE=true

# Development only
DISABLE_AUTH_FOR_TESTING=true  # NEVER use in production
```

This OAuth implementation ensures seamless integration with Claude's MCP ecosystem while maintaining high security standards through modern OAuth practices.