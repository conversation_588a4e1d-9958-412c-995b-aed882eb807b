#!/bin/bash
# Search Server Launcher Script
# This script provides a simple menu to launch the search server in different modes

# Set default port
PORT=${1:-8511}
CLEAR="\033[0m"
GREEN="\033[32m"
YELLOW="\033[33m"
CYAN="\033[36m"
RED="\033[31m"
BOLD="\033[1m"
#DOTENV_KEY='dotenv://:<EMAIL>/vault/.env.vault?environment=development'
export DOTENV_KEY='dotenv://:<EMAIL>/vault/.env.vault?environment=development'
#export CLAUDE_CLIENT_ID=08407767-ff92-4c28-b15f-32186412aacd

# Display header
echo -e "${BOLD}${CYAN}"
echo "======================================"
echo "  Search Server Launcher"
echo "======================================"
echo -e "${CLEAR}"

# Display menu
echo -e "${BOLD}Select a server mode:${CLEAR}"
echo ""
echo -e "${GREEN}1) Direct OAuth Server with Google OAuth ${BOLD}(Recommended)${CLEAR}${GREEN}"
echo "   Full Google OAuth authentication with direct tool registration"
echo "   Ideal for production use with Claude"
echo -e "${CLEAR}"
echo -e "${YELLOW}2) Direct OAuth Server without Google OAuth${CLEAR}"
echo "   OAuth-compatible but without Google authentication"
echo "   For simplified deployment where Google OAuth isn't needed"
echo -e "${CLEAR}"
echo -e "${CYAN}3) StreamableHTTP Server with Google OAuth${CLEAR}"
echo "   Full Google OAuth with StreamableHTTP transport for better"
echo "   streaming support and resumability"
echo -e "${CLEAR}"
echo -e "${RED}4) Direct Server (No Auth)${CLEAR}"
echo "   No authentication, direct tool registration"
echo "   For testing only - NOT SECURE"
echo -e "${CLEAR}"
echo ""
echo -n "Enter selection [1]: "
read CHOICE

# Default to option 1
CHOICE=${CHOICE:-1}

case $CHOICE in
  1)
    # Direct OAuth Server with Google OAuth (Default)
    echo -e "${GREEN}Starting Direct OAuth Server with Google OAuth on port $PORT${CLEAR}"
    export DISABLE_ALL_AUTH="false"
    export GOOGLE_OAUTH_ENABLE="true"
    export STREAMABLE_HTTP_ENABLE="false"
    ;;
  2)
    # Direct OAuth Server without Google OAuth
    echo -e "${YELLOW}Starting Direct OAuth Server without Google OAuth on port $PORT${CLEAR}"
    export DISABLE_ALL_AUTH="false"
    export GOOGLE_OAUTH_ENABLE="false"
    export STREAMABLE_HTTP_ENABLE="false"
    ;;
  3)
    # StreamableHTTP Server with Google OAuth
    echo -e "${CYAN}Starting StreamableHTTP Server with Google OAuth on port $PORT${CLEAR}"
    export DISABLE_ALL_AUTH="false"
    export GOOGLE_OAUTH_ENABLE="true"
    export STREAMABLE_HTTP_ENABLE="true"
    ;;
  4)
    # Direct Server (No Auth)
    echo -e "${RED}Starting Direct Server without authentication on port $PORT${CLEAR}"
    echo -e "${RED}WARNING: This mode is insecure and should only be used for testing!${CLEAR}"
    export DISABLE_ALL_AUTH="true"
    export GOOGLE_OAUTH_ENABLE="false"
    export STREAMABLE_HTTP_ENABLE="false"
    ;;
  *)
    echo "Invalid selection, defaulting to Direct OAuth Server with Google OAuth"
    export DISABLE_ALL_AUTH="false"
    export GOOGLE_OAUTH_ENABLE="true"
    export STREAMABLE_HTTP_ENABLE="false"
    ;;
esac

# Determine transport type based on selections
TRANSPORT="sse"
if [ "$STREAMABLE_HTTP_ENABLE" = "true" ]; then
  TRANSPORT="streamable"
fi

# Run the server with selected configuration
echo "Starting server on port $PORT with transport $TRANSPORT..."
uv run search-server --port $PORT --transport $TRANSPORT
