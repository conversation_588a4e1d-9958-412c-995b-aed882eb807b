# Search Server Architecture

This document describes the server architecture of the search server, which provides MCP (Model Context Protocol) compatible tools to Claude and other AI models.

## Server Implementations

The search server implements two server types to accommodate different use cases:

### 1. Direct OAuth Server (Default & Recommended)

**File:** `direct_oauth_server.py`  
**Used by default**

This is the **recommended implementation** for production use with <PERSON>. It combines the direct server pattern (which reliably exposes tools to <PERSON>) with OAuth authentication for security.

- **Pros:** 
  - Reliable tool registration with Claude
  - OAuth security
  - Special handling for <PERSON>'s authentication flow
  - CORS support
  - Automatic token generation for compatibility
  
- **Key Features:**
  - Uses a robust tool registration pattern proven to work with <PERSON>
  - <PERSON>mple<PERSON> a simplified OAuth provider with <PERSON> compatibility
  - Provides automatic token validation
  - Creates direct tokens without requiring valid auth codes

- **Use Case:** Production environments requiring security and Claude compatibility

### 2. Direct Server (No OAuth)

**File:** `direct_server.py`  
**Environment Variable:** `DISABLE_ALL_AUTH=true`

This implementation provides a direct connection to <PERSON> without any authentication requirements. It uses the same direct server pattern but without OAuth security.

- **Pros:** Simple, reliable tool registration with <PERSON>
- **Cons:** No authentication, not secure for production
- **Use Case:** Development and testing environments only

## Authentication Flows

The server supports two authentication flows:

1. **No Authentication** - When `DISABLE_ALL_AUTH=true`, no authentication is required
2. **OAuth Authentication** - Full OAuth 2.0 flow with the following endpoints:
   - `/authorize` - For authorization code generation
   - `/token` - For token exchange
   - `/oauth/callback` - For OAuth callback handling
   - `/.well-known/oauth-authorization-server` - For OAuth discovery

## Server Selection Logic

The server implementation is selected based on a simple logic:

1. If `DISABLE_ALL_AUTH=true`, use the Direct Server without authentication
2. Otherwise, use the Direct OAuth Server (default)

## Recommended Configuration

For production use with Claude, the recommended configuration is to use the default settings which will automatically use the Direct OAuth Server implementation, providing the best balance of security and Claude compatibility.

No special environment variables are needed to enable this configuration as it's the default.

## Implementation Details

### Tool Registration

The Direct OAuth Server uses the following pattern for tool registration, which is proven to work with Claude:

```python
@server.list_tools()
async def handle_list_tools() -> list[types.Tool]:
    """List available tools."""
    return [
        types.Tool(
            name="tool_name",
            description="Tool description",
            inputSchema={...},
        ),
        # More tools...
    ]
```

### OAuth Provider

The Direct OAuth Server implements a simplified OAuth provider with special handling for Claude:

```python
class SimpleOAuthProvider(OAuthAuthorizationServerProvider):
    # Simplified OAuth implementation
    # Automatically generates valid tokens
    # Special handling for Claude requests
```

## Debugging and Monitoring

The server provides detailed logging for debugging and monitoring:

- All servers log connection and request information
- OAuth flows are logged in detail
- Tool calls and responses are tracked

For debugging, use the `debug_server.py` script which enables IDE integration with breakpoints and variable inspection.