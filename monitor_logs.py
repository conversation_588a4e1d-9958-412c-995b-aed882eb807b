#!/usr/bin/env python3
import os
import time
import sys
import logging
import glob
from termcolor import colored

# Configure logging for the monitor
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    stream=sys.stdout
)
logger = logging.getLogger("monitor")

def get_log_files():
    """Get a list of all log files in the logs directory"""
    project_root = os.path.dirname(os.path.abspath(__file__))
    logs_dir = os.path.join(project_root, "logs")
    
    # Create the logs directory if it doesn't exist
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir)
        logger.info(f"Created logs directory: {logs_dir}")
    
    # Find all log files
    log_files = glob.glob(os.path.join(logs_dir, "*.log"))
    return log_files

def tail_file(file_path, follow=True, lines=10):
    """
    Read the last N lines of a file, with optional follow mode.
    
    Args:
        file_path: Path to the file to read
        follow: If True, continue reading new lines as they are added
        lines: Number of lines to read initially
    """
    if not os.path.exists(file_path):
        logger.error(f"File does not exist: {file_path}")
        return
    
    logger.info(f"Monitoring log file: {file_path}")
    logger.info(f"Press Ctrl+C to exit")
    print("-" * 80)
    
    # Initial read of last N lines
    with open(file_path, 'r') as f:
        content = f.readlines()
        last_lines = content[-lines:] if lines < len(content) else content
        for line in last_lines:
            print(line.strip())
    
    # Follow mode - continue reading new lines
    if follow:
        last_position = os.path.getsize(file_path)
        try:
            while True:
                current_size = os.path.getsize(file_path)
                if current_size > last_position:
                    with open(file_path, 'r') as f:
                        f.seek(last_position)
                        for line in f:
                            # Color-code lines for better visibility
                            line = line.strip()
                            if 'ERROR' in line or 'error' in line.lower():
                                print(colored(line, 'red'))
                            elif 'WARNING' in line or 'warning' in line.lower():
                                print(colored(line, 'yellow'))
                            elif 'TOKEN DEBUG' in line:
                                print(colored(line, 'cyan'))
                            elif 'SUCCESS' in line or 'successfully' in line.lower():
                                print(colored(line, 'green'))
                            else:
                                print(line)
                    
                    last_position = current_size
                
                time.sleep(0.1)
        except KeyboardInterrupt:
            logger.info("Monitoring stopped by user")
            return

def list_log_files():
    """List all available log files"""
    log_files = get_log_files()
    
    if not log_files:
        print("No log files found in logs directory")
        return
    
    print("\nAvailable log files:")
    for i, log_file in enumerate(log_files, 1):
        file_name = os.path.basename(log_file)
        file_size = os.path.getsize(log_file)
        size_str = f"{file_size / 1024:.1f} KB" if file_size < 1024 * 1024 else f"{file_size / (1024 * 1024):.1f} MB"
        mod_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(os.path.getmtime(log_file)))
        print(f"{i}. {file_name} ({size_str}, modified: {mod_time})")
    print()

def select_log_file_interactive():
    """Allow the user to select a log file interactively"""
    log_files = get_log_files()
    
    if not log_files:
        logger.error("No log files found in logs directory")
        return None
    
    print("\nAvailable log files:")
    for i, log_file in enumerate(log_files, 1):
        file_name = os.path.basename(log_file)
        file_size = os.path.getsize(log_file)
        size_str = f"{file_size / 1024:.1f} KB" if file_size < 1024 * 1024 else f"{file_size / (1024 * 1024):.1f} MB"
        mod_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(os.path.getmtime(log_file)))
        print(f"{i}. {file_name} ({size_str}, modified: {mod_time})")
    
    while True:
        try:
            choice = input("\nEnter the number of the log file to monitor (or 'q' to quit): ")
            if choice.lower() == 'q':
                return None
            
            choice = int(choice)
            if 1 <= choice <= len(log_files):
                return log_files[choice-1]
            else:
                print(f"Please enter a number between 1 and {len(log_files)}")
        except ValueError:
            print("Please enter a valid number")

if __name__ == "__main__":
    import argparse
    
    # Get all available log files
    available_logs = get_log_files()
    default_log = os.path.join('logs', 'search_server.log')
    if os.path.exists(default_log):
        default_log_file = default_log
    elif available_logs:
        default_log_file = available_logs[0]
    else:
        default_log_file = os.path.join('logs', 'search_server.log')
    
    parser = argparse.ArgumentParser(description="Monitor search server logs")
    parser.add_argument(
        "--log-file", 
        default=None, 
        help="Path to the log file to monitor"
    )
    parser.add_argument(
        "--lines", 
        type=int, 
        default=10, 
        help="Number of lines to show initially"
    )
    parser.add_argument(
        "--no-follow", 
        action="store_true", 
        help="Don't follow the file for new entries"
    )
    parser.add_argument(
        "--list", 
        action="store_true", 
        help="List available log files"
    )
    parser.add_argument(
        "--component",
        help="Component name to monitor (e.g., search_engine, web_scraper)"
    )
    parser.add_argument(
        "--interactive",
        "-i",
        action="store_true",
        help="Select log file interactively"
    )
    
    args = parser.parse_args()
    
    if args.list:
        list_log_files()
        sys.exit(0)
    
    log_file = args.log_file
    
    # Handle component selection
    if args.component:
        component_log = os.path.join('logs', f"{args.component}.log")
        if os.path.exists(component_log):
            log_file = component_log
        else:
            logger.warning(f"Component log file not found: {component_log}")
            logger.info("Available log files:")
            list_log_files()
            sys.exit(1)
    
    # Handle interactive selection
    if args.interactive:
        selected_file = select_log_file_interactive()
        if selected_file:
            log_file = selected_file
        else:
            logger.info("No file selected, exiting")
            sys.exit(0)
    
    # If no log file specified, use default
    if not log_file:
        log_file = default_log_file
    
    tail_file(
        log_file, 
        follow=not args.no_follow, 
        lines=args.lines
    )