# OAuth Implementation Guide

This document provides detailed information about the OAuth implementation in the search_server MCP application.

## Overview

The authentication system uses Google OAuth 2.0 with modern OAuth standards to authenticate users and provide secure access to the MCP (Model Context Protocol) server. The implementation includes:

### Key Features

- **Dynamic Client Registration (RFC 7591)**: Automatic client registration for MCP clients
- **PKCE S256 Support (RFC 7636)**: Enhanced security with Proof Key for Code Exchange
- **Google OAuth 2.0 Integration**: Standard OAuth flow with Google authentication
- **Enhanced Error Handling**: Comprehensive logging and debugging capabilities
- **MCP Client Compatibility**: Full support for Claude Desktop, Claude Code, and custom clients

### OAuth Flow

1. **Client Discovery**: MCP client discovers OAuth endpoints via `/.well-known/oauth-authorization-server`
2. **Dynamic Registration**: Client automatically registers via `/register` endpoint
3. **Authorization**: User is redirected to Google for authentication
4. **Token Exchange**: Server exchanges authorization code for tokens (with PKCE verification)
5. **API Access**: Client uses tokens for authenticated API requests

## Setup

### 1. Google Cloud Console Setup

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Navigate to "APIs & Services" > "Credentials"
4. Click "Create Credentials" > "OAuth client ID"
5. Set application type to "Web application"
6. Add authorized redirect URIs:
   - `https://your-domain.com/oauth/callback`
   - `https://your-domain.com/google/callback`
7. Note your Client ID and Client Secret

### 2. Environment Variables

Add the following to your `.env` file:

```
GOOGLE_OAUTH_CLIENT_ID=your-client-id
GOOGLE_OAUTH_CLIENT_SECRET=your-client-secret
GOOGLE_OAUTH_REDIRECT_URI=https://your-domain.com/oauth/callback
```

For testing, you can also add:

```
DISABLE_AUTH_FOR_TESTING=true
ALLOWED_GOOGLE_EMAIL=<EMAIL>,<EMAIL>  # Comma-separated list
```

This will bypass authentication checks, but should never be used in production.

## RFC Compliance

### Dynamic Client Registration (RFC 7591)

The server implements RFC 7591 for automatic client registration:

**Registration Endpoint**: `POST /register`

**Request Example**:
```json
{
  "client_name": "My MCP Client",
  "redirect_uris": ["http://localhost:8080/callback"],
  "grant_types": ["authorization_code", "refresh_token"],
  "response_types": ["code"],
  "scope": "claudeai read write profile admin"
}
```

**Response Example**:
```json
{
  "client_id": "client_abc123...",
  "client_secret": "secret_xyz789...",
  "client_name": "My MCP Client",
  "redirect_uris": ["http://localhost:8080/callback"],
  "grant_types": ["authorization_code", "refresh_token"],
  "response_types": ["code"],
  "scope": "claudeai read write profile admin",
  "token_endpoint_auth_method": "client_secret_post",
  "client_id_issued_at": 1640995200,
  "client_secret_expires_at": 0
}
```

### PKCE Support (RFC 7636)

The server supports Proof Key for Code Exchange for enhanced security:

**Supported Methods**: `S256` (recommended), `plain`
**Discovery Field**: `"code_challenge_methods_supported": ["S256", "plain"]`

**Authorization with PKCE**:
```
GET /authorize?response_type=code&client_id=client_123&code_challenge=abc123&code_challenge_method=S256&redirect_uri=...
```

**Token Exchange with PKCE**:
```
POST /token
Content-Type: application/x-www-form-urlencoded

grant_type=authorization_code&code=auth_code_123&code_verifier=xyz789&client_id=client_123
```

## Authentication Flow Details

### Server-Side Implementation

The server implements these key components:

1. **GoogleOAuthProvider**: Implements the OAuthAuthorizationServerProvider interface from MCP
2. **GoogleCallbackHandler**: Handles OAuth redirects from Google
3. **AuthErrorHandler**: Provides user-friendly error pages

The authentication flow works as follows:

1. User visits `/auth` endpoint
2. Server redirects to Google OAuth with appropriate parameters
3. User authenticates with Google
4. Google redirects back to `/oauth/callback` with an authorization code
5. Server exchanges this code for Google tokens
6. Server creates MCP tokens associated with the Google identity
7. Client uses these MCP tokens for subsequent API calls

### Client-Side Implementation

Two client examples are provided:

1. **client_test_oauth.py**: Demonstrates basic authentication and API calls
2. **sse_client_oauth.py**: Shows how to maintain an authenticated SSE connection

These clients:
- Open a browser for authentication
- Store tokens locally for reuse
- Include tokens in API calls

## Testing and Debugging

### Debug Endpoints

The server includes several debug endpoints:

- `/auth`: Authentication page with Google login button
- `/auth/debug`: Shows OAuth status information
- `/debug-sse`: Test SSE without authentication (for debugging)

### Common Issues

1. **Redirect URI Mismatch**: Ensure the URI in Google Console matches your .env file exactly
2. **Invalid Client ID/Secret**: Double-check credentials in .env file
3. **Token Exchange Failures**: Check for error messages in server logs

## Production Considerations

For production deployment:

1. **Token Storage**: Replace in-memory storage with a persistent database
2. **Security Hardening**:
   - Use HTTPS for all endpoints
   - Implement proper CSRF protection
   - Set secure and httpOnly cookie flags
3. **User Management**:
   - Add user registration and profile storage
   - Implement role-based access control
4. **Token Refresh**:
   - Add automatic refresh of expired tokens
   - Implement token revocation on logout

## API Reference

### OAuth Endpoints

#### Core OAuth Endpoints
- **GET /.well-known/oauth-authorization-server**: OAuth discovery document (RFC 8414)
- **POST /register**: Dynamic client registration (RFC 7591)
- **GET /authorize**: OAuth authorization endpoint (with PKCE support)
- **POST /token**: Token exchange endpoint (RFC 6749 + RFC 7636)
- **POST /revoke**: Token revocation endpoint

#### Authentication Pages
- **GET /auth**: Authentication page
- **GET /oauth/callback**: Main OAuth callback handler
- **GET /google/callback**: Alternative callback path
- **GET /auth/error**: Error display page
- **GET /auth/debug**: Debug information

#### Discovery Document Example
```json
{
  "issuer": "https://your-domain.com",
  "authorization_endpoint": "https://your-domain.com/authorize",
  "token_endpoint": "https://your-domain.com/token",
  "registration_endpoint": "https://your-domain.com/register",
  "revocation_endpoint": "https://your-domain.com/revoke",
  "code_challenge_methods_supported": ["S256", "plain"],
  "token_endpoint_auth_methods_supported": ["client_secret_basic", "client_secret_post", "none"],
  "scopes_supported": ["claudeai", "read", "write", "profile", "admin"],
  "response_types_supported": ["code"],
  "grant_types_supported": ["authorization_code", "refresh_token"],
  "dynamic_client_registration_supported": true
}
```

### Protected API Endpoints

- **GET /sse**: Server-sent events endpoint (requires authentication)
- **POST /api/tools**: Call MCP tools (requires authentication)
- **POST /api/generate**: Generate content (requires authentication)

## Client Integration

To integrate with your own client:

1. Redirect users to `/auth` for authentication
2. After successful authentication, store the provided tokens
3. Include the access token in all API requests:
   ```
   Authorization: Bearer {access_token}
   ```
4. Implement token refresh when the access token expires 